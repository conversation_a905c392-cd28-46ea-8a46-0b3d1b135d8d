# 心理咨询服务平台 - 数据库设计

## 📋 概述

基于后端已实现功能设计的完整数据库结构，支持用户管理、咨询师认证、预约系统、咨询会话等核心业务功能。

## 🗄️ 数据库结构

### 核心表结构

#### 1. 用户表 (users)
- **主要功能**：存储平台所有用户的基本信息
- **关键字段**：
  - `id` - 主键
  - `username` - 用户名（唯一）
  - `email` - 邮箱（唯一）
  - `password` - 加密密码
  - `real_name` - 真实姓名
  - `phone` - 手机号
  - `status` - 用户状态（ACTIVE/INACTIVE/BANNED）
  - `email_verified` - 邮箱验证状态
  - `phone_verified` - 手机验证状态

#### 2. 咨询师表 (counselors)
- **主要功能**：存储咨询师的专业信息和认证状态
- **关键字段**：
  - `id` - 主键
  - `user_id` - 关联用户ID
  - `license_number` - 执业证书编号（唯一）
  - `specialization` - 专业领域（枚举）
  - `professional_summary` - 专业简介
  - `hourly_rate` - 咨询费率
  - `status` - 认证状态（PENDING/APPROVED/REJECTED/SUSPENDED）
  - `available_for_booking` - 是否可预约
  - `rating` - 平均评分
  - `total_sessions` - 总咨询次数

#### 3. 预约表 (appointments)
- **主要功能**：管理用户与咨询师的预约信息
- **关键字段**：
  - `id` - 主键
  - `user_id` - 用户ID
  - `counselor_id` - 咨询师ID
  - `scheduled_time` - 预约时间
  - `actual_start_time` - 实际开始时间
  - `actual_end_time` - 实际结束时间
  - `fee` - 咨询费用
  - `status` - 预约状态（PENDING/CONFIRMED/IN_PROGRESS/COMPLETED/CANCELLED/NO_SHOW）
  - `cancellation_reason` - 取消原因
  - `cancelled_by` - 取消方

#### 4. 咨询会话表 (counseling_sessions)
- **主要功能**：记录具体的咨询会话内容和反馈
- **关键字段**：
  - `id` - 主键
  - `appointment_id` - 关联预约ID
  - `start_time` - 开始时间
  - `end_time` - 结束时间
  - `session_notes` - 会话记录
  - `counselor_notes` - 咨询师备注
  - `user_feedback` - 用户反馈
  - `user_rating` - 用户评分（1-5）
  - `is_emergency` - 是否紧急情况
  - `follow_up_required` - 是否需要跟进

#### 5. 验证码表 (verification_codes)
- **主要功能**：管理邮箱、手机验证码和密码重置
- **关键字段**：
  - `id` - 主键
  - `user_id` - 用户ID（可为空）
  - `email` - 邮箱
  - `phone` - 手机号
  - `code` - 验证码
  - `type` - 验证类型
  - `used` - 是否已使用
  - `expires_at` - 过期时间

#### 6. 系统配置表 (system_configs)
- **主要功能**：存储系统配置参数
- **关键字段**：
  - `config_key` - 配置键（唯一）
  - `config_value` - 配置值
  - `description` - 配置描述

#### 7. 操作日志表 (operation_logs)
- **主要功能**：记录用户操作日志，用于审计和分析
- **关键字段**：
  - `user_id` - 操作用户ID
  - `operation_type` - 操作类型
  - `operation_desc` - 操作描述
  - `ip_address` - IP地址
  - `request_url` - 请求URL
  - `response_status` - 响应状态码

## 🔗 表关系

```
users (1) ←→ (0..1) counselors
users (1) ←→ (0..n) appointments
counselors (1) ←→ (0..n) appointments
appointments (1) ←→ (0..1) counseling_sessions
users (1) ←→ (0..n) verification_codes
users (1) ←→ (0..n) operation_logs
```

## 📊 专业领域枚举

```sql
ENUM(
    'ANXIETY_DISORDERS',      -- 焦虑障碍
    'DEPRESSION',             -- 抑郁症
    'RELATIONSHIP_COUNSELING', -- 情感咨询
    'FAMILY_THERAPY',         -- 家庭治疗
    'CHILD_PSYCHOLOGY',       -- 儿童心理
    'ADDICTION_COUNSELING',   -- 成瘾咨询
    'TRAUMA_THERAPY',         -- 创伤治疗
    'CAREER_COUNSELING',      -- 职业咨询
    'STRESS_MANAGEMENT',      -- 压力管理
    'GENERAL_COUNSELING'      -- 综合咨询
)
```

## 🚀 部署说明

### 1. 创建数据库
```bash
mysql -u root -p < schema.sql
```

### 2. 创建索引（性能优化）
```bash
mysql -u root -p psychology_platform < indexes.sql
```

### 3. 创建视图（简化查询）
```bash
mysql -u root -p psychology_platform < views.sql
```

### 4. 插入示例数据（可选）
```bash
mysql -u root -p psychology_platform < sample_data.sql
```

## 🔍 核心索引

### 高频查询索引
- **用户登录**：`idx_users_login`, `idx_users_email_login`
- **咨询师搜索**：`idx_counselors_search`, `idx_counselors_price_range`
- **预约查询**：`idx_appointments_user_time`, `idx_appointments_counselor_time`
- **会话记录**：`idx_sessions_user_history`, `idx_sessions_counselor_history`

### 业务逻辑索引
- **时间冲突检查**：`idx_appointments_conflict`
- **今日预约**：`idx_appointments_today`
- **跟进会话**：`idx_sessions_follow_up`
- **紧急情况**：`idx_sessions_emergency`

## 📈 数据库视图

### 业务视图
- `v_user_profile` - 用户基本信息（隐藏敏感数据）
- `v_counselor_detail` - 咨询师详细信息
- `v_available_counselors` - 可预约咨询师
- `v_appointment_detail` - 预约详细信息
- `v_session_detail` - 会话详细信息

### 统计视图
- `v_user_stats` - 用户统计数据
- `v_counselor_stats` - 咨询师统计数据
- `v_platform_stats` - 平台整体统计

### 业务查询视图
- `v_sessions_need_follow_up` - 需要跟进的会话
- `v_today_appointments` - 今日预约
- `v_upcoming_appointments` - 即将开始的预约

## 🔧 配置参数

### 系统默认配置
```sql
platform_name: '心理咨询服务平台'
max_appointment_advance_days: 30
min_cancellation_hours: 24
default_session_duration: 60
counselor_approval_required: true
email_verification_required: true
verification_code_expiry_minutes: 10
```

## 📊 示例数据

数据库包含完整的示例数据：
- **5个普通用户**：包含不同性别、年龄的用户
- **5个咨询师**：涵盖不同专业领域
- **10个预约记录**：包含各种状态的预约
- **5个咨询会话**：包含完整的会话记录和反馈
- **操作日志**：用户行为记录

## 🔒 安全考虑

### 数据安全
- 密码使用BCrypt加密存储
- 敏感信息通过视图隐藏
- 外键约束保证数据完整性
- 操作日志记录用户行为

### 性能优化
- 合理的索引设计
- 分区表支持（大数据量场景）
- 查询优化建议
- 定期维护脚本

## 📝 维护建议

### 定期维护
```sql
-- 清理过期验证码
DELETE FROM verification_codes WHERE expires_at < NOW() AND used = TRUE;

-- 优化表
OPTIMIZE TABLE appointments, counseling_sessions, operation_logs;

-- 分析表统计信息
ANALYZE TABLE users, counselors, appointments, counseling_sessions;
```

### 监控指标
- 数据库连接数
- 慢查询日志
- 索引使用率
- 表空间使用情况

## 🔄 扩展建议

### 功能扩展
- 消息通知表
- 文件上传表
- 支付记录表
- 评价标签表
- 咨询师排班表

### 性能扩展
- 读写分离
- 数据分片
- 缓存策略
- 归档策略

这个数据库设计完全基于后端已实现的功能，支持所有核心业务场景，具备良好的扩展性和性能优化。
