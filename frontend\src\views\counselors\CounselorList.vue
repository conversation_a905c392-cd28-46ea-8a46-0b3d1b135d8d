<template>
  <div class="counselor-list-page">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="nav-container">
        <router-link to="/" class="nav-brand">
          <h2>心理咨询平台</h2>
        </router-link>
        <div class="nav-menu">
          <router-link to="/" class="nav-link">首页</router-link>
          <template v-if="!userStore.isLoggedIn">
            <router-link to="/login" class="nav-link">登录</router-link>
            <router-link to="/register" class="nav-link nav-button">注册</router-link>
          </template>
          <template v-else>
            <router-link to="/dashboard" class="nav-link">个人中心</router-link>
            <el-button @click="handleLogout" type="text">退出</el-button>
          </template>
        </div>
      </div>
    </nav>

    <div class="page-container">
      <div class="content-wrapper">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1>寻找专业咨询师</h1>
          <p>选择最适合您的心理健康专家</p>
        </div>

        <!-- 搜索和筛选 -->
        <div class="search-section card">
          <el-form :model="searchForm" :inline="true" class="search-form">
            <el-form-item label="专业领域">
              <el-select 
                v-model="searchForm.specialization" 
                placeholder="选择专业领域"
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="(label, value) in specializationOptions"
                  :key="value"
                  :label="label"
                  :value="value"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="费用范围">
              <el-slider
                v-model="searchForm.priceRange"
                range
                :min="0"
                :max="1000"
                :step="50"
                style="width: 200px"
                @change="handlePriceChange"
              />
              <span class="price-display">
                ¥{{ searchForm.priceRange[0] }} - ¥{{ searchForm.priceRange[1] }}
              </span>
            </el-form-item>
            
            <el-form-item label="最低评分">
              <el-rate
                v-model="searchForm.minRating"
                :max="5"
                show-score
                text-color="#ff9900"
                score-template="{value}分以上"
              />
            </el-form-item>
            
            <el-form-item label="经验年限">
              <el-select 
                v-model="searchForm.minYears" 
                placeholder="选择经验年限"
                clearable
                style="width: 150px"
              >
                <el-option label="1年以上" :value="1" />
                <el-option label="3年以上" :value="3" />
                <el-option label="5年以上" :value="5" />
                <el-option label="10年以上" :value="10" />
              </el-select>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="handleSearch" :loading="loading">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 排序选项 -->
        <div class="sort-section">
          <span>排序方式：</span>
          <el-radio-group v-model="sortBy" @change="handleSort">
            <el-radio-button label="rating">评分最高</el-radio-button>
            <el-radio-button label="sessions">最受欢迎</el-radio-button>
            <el-radio-button label="price_asc">价格从低到高</el-radio-button>
            <el-radio-button label="price_desc">价格从高到低</el-radio-button>
          </el-radio-group>
        </div>

        <!-- 咨询师列表 -->
        <div class="counselors-section" v-loading="loading">
          <div class="counselors-grid">
            <div 
              v-for="counselor in counselors" 
              :key="counselor.id"
              class="counselor-card card"
              @click="goToCounselorDetail(counselor.id)"
            >
              <div class="counselor-header">
                <el-avatar :size="80" :src="counselor.user?.avatarUrl">
                  {{ counselor.user?.username?.charAt(0) }}
                </el-avatar>
                <div class="counselor-basic">
                  <h3>{{ counselor.user?.realName || counselor.user?.username }}</h3>
                  <p class="specialization">{{ getSpecializationText(counselor.specialization) }}</p>
                  <div class="rating">
                    <el-rate 
                      :model-value="counselor.rating || 0" 
                      disabled 
                      show-score 
                      text-color="#ff9900"
                    />
                    <span class="sessions-count">({{ counselor.totalSessions }}次咨询)</span>
                  </div>
                </div>
                <div class="counselor-price">
                  <span class="price">¥{{ counselor.hourlyRate }}</span>
                  <span class="price-unit">/小时</span>
                </div>
              </div>
              
              <div class="counselor-info">
                <div class="info-item">
                  <el-icon><Trophy /></el-icon>
                  <span>{{ counselor.yearsOfExperience || 0 }}年经验</span>
                </div>
                <div class="info-item">
                  <el-icon><School /></el-icon>
                  <span>{{ counselor.education || '暂无信息' }}</span>
                </div>
              </div>
              
              <div class="counselor-summary">
                <p>{{ counselor.professionalSummary || '暂无简介' }}</p>
              </div>
              
              <div class="counselor-actions">
                <el-button 
                  type="primary" 
                  :disabled="!counselor.availableForBooking"
                  @click.stop="handleBooking(counselor.id)"
                >
                  {{ counselor.availableForBooking ? '立即预约' : '暂不可约' }}
                </el-button>
                <el-button @click.stop="goToCounselorDetail(counselor.id)">
                  查看详情
                </el-button>
              </div>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-if="!loading && counselors.length === 0" class="empty-state">
            <el-empty description="暂无符合条件的咨询师">
              <el-button type="primary" @click="handleReset">重置筛选条件</el-button>
            </el-empty>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-section" v-if="total > 0">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[12, 24, 48]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { searchCounselors, getAvailableCounselors } from '@/api/counselor'
import { ElMessage } from 'element-plus'
import { Search, Trophy, School } from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const counselors = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(12)
const sortBy = ref('rating')

// 搜索表单
const searchForm = reactive({
  specialization: '',
  priceRange: [0, 1000],
  minRating: 0,
  minYears: null
})

// 专业领域选项
const specializationOptions = {
  'ANXIETY_DISORDERS': '焦虑障碍',
  'DEPRESSION': '抑郁症',
  'RELATIONSHIP_COUNSELING': '情感咨询',
  'FAMILY_THERAPY': '家庭治疗',
  'CHILD_PSYCHOLOGY': '儿童心理',
  'ADDICTION_COUNSELING': '成瘾咨询',
  'TRAUMA_THERAPY': '创伤治疗',
  'CAREER_COUNSELING': '职业咨询',
  'STRESS_MANAGEMENT': '压力管理',
  'GENERAL_COUNSELING': '综合咨询'
}

const getSpecializationText = (specialization) => {
  return specializationOptions[specialization] || specialization
}

// 获取咨询师列表
const fetchCounselors = async () => {
  try {
    loading.value = true
    
    const params = {
      page: currentPage.value - 1,
      size: pageSize.value
    }
    
    // 添加搜索条件
    if (searchForm.specialization) {
      params.specialization = searchForm.specialization
    }
    if (searchForm.priceRange[0] > 0 || searchForm.priceRange[1] < 1000) {
      params.minRate = searchForm.priceRange[0]
      params.maxRate = searchForm.priceRange[1]
    }
    if (searchForm.minRating > 0) {
      params.minRating = searchForm.minRating
    }
    if (searchForm.minYears) {
      params.minYears = searchForm.minYears
    }
    
    const response = await searchCounselors(params)
    counselors.value = response.content || response.data?.content || []
    total.value = response.totalElements || response.data?.totalElements || 0
  } catch (error) {
    console.error('Failed to fetch counselors:', error)
    ElMessage.error('获取咨询师列表失败')
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchCounselors()
}

// 重置搜索
const handleReset = () => {
  searchForm.specialization = ''
  searchForm.priceRange = [0, 1000]
  searchForm.minRating = 0
  searchForm.minYears = null
  sortBy.value = 'rating'
  currentPage.value = 1
  fetchCounselors()
}

// 处理价格变化
const handlePriceChange = () => {
  // 价格变化时自动搜索
  handleSearch()
}

// 处理排序
const handleSort = () => {
  // 这里可以根据sortBy的值来重新排序
  fetchCounselors()
}

// 分页处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchCounselors()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchCounselors()
}

// 跳转到咨询师详情
const goToCounselorDetail = (id) => {
  router.push(`/counselors/${id}`)
}

// 处理预约
const handleBooking = (counselorId) => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  router.push(`/booking/${counselorId}`)
}

// 处理登出
const handleLogout = async () => {
  await userStore.logoutUser()
  router.push('/')
}

onMounted(() => {
  fetchCounselors()
})
</script>

<style lang="scss" scoped>
// 导航栏样式（复用首页样式）
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.nav-brand {
  text-decoration: none;
  
  h2 {
    color: var(--primary-color);
    font-weight: 600;
  }
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 30px;
}

.nav-link {
  color: var(--text-regular);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  
  &:hover {
    color: var(--primary-color);
  }
  
  &.nav-button {
    background: var(--primary-color);
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    
    &:hover {
      background: var(--primary-dark);
      color: white;
    }
  }
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  
  h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: white;
    margin-bottom: 10px;
  }
  
  p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
  }
}

.search-section {
  margin-bottom: 30px;
  padding: 30px;
}

.search-form {
  .el-form-item {
    margin-bottom: 20px;
  }
}

.price-display {
  margin-left: 15px;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.sort-section {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
  
  span {
    color: white;
    font-weight: 500;
  }
}

.counselors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.counselor-card {
  padding: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

.counselor-header {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 20px;
}

.counselor-basic {
  flex: 1;
  
  h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
  }
  
  .specialization {
    color: var(--text-secondary);
    margin-bottom: 10px;
  }
  
  .rating {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .sessions-count {
    color: var(--text-secondary);
    font-size: 0.9rem;
  }
}

.counselor-price {
  text-align: right;
  
  .price {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
  }
  
  .price-unit {
    color: var(--text-secondary);
    font-size: 0.9rem;
  }
}

.counselor-info {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 0.9rem;
  
  .el-icon {
    color: var(--primary-color);
  }
}

.counselor-summary {
  margin-bottom: 20px;
  
  p {
    color: var(--text-regular);
    line-height: 1.6;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.counselor-actions {
  display: flex;
  gap: 15px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

// 响应式设计
@media (max-width: 768px) {
  .counselors-grid {
    grid-template-columns: 1fr;
  }
  
  .counselor-header {
    flex-direction: column;
    text-align: center;
  }
  
  .counselor-price {
    text-align: center;
  }
  
  .counselor-info {
    justify-content: center;
  }
  
  .search-form {
    .el-form-item {
      display: block;
      margin-bottom: 15px;
    }
  }
  
  .sort-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>
