<template>
  <div class="dashboard">
    <!-- 侧边栏 -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <div class="user-info">
          <el-avatar :size="60" :src="userStore.userAvatar">
            {{ userStore.userName.charAt(0) }}
          </el-avatar>
          <div class="user-details">
            <h3>{{ userStore.userInfo?.realName || userStore.userName }}</h3>
            <p>{{ userStore.isCounselor ? '咨询师' : '用户' }}</p>
          </div>
        </div>
      </div>
      
      <nav class="sidebar-nav">
        <router-link 
          v-for="item in menuItems" 
          :key="item.name"
          :to="item.path"
          class="nav-item"
          :class="{ active: $route.name === item.name }"
        >
          <el-icon>
            <component :is="item.icon" />
          </el-icon>
          <span>{{ item.label }}</span>
        </router-link>
      </nav>
      
      <div class="sidebar-footer">
        <el-button @click="handleLogout" class="logout-btn">
          <el-icon><SwitchButton /></el-icon>
          退出登录
        </el-button>
      </div>
    </aside>
    
    <!-- 主内容区 -->
    <main class="main-content">
      <!-- 顶部导航 -->
      <header class="content-header">
        <div class="header-left">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>
              <router-link to="/">首页</router-link>
            </el-breadcrumb-item>
            <el-breadcrumb-item>个人中心</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <el-button @click="goHome" type="text">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
        </div>
      </header>
      
      <!-- 页面内容 -->
      <div class="content-body">
        <router-view />
      </div>
    </main>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { 
  House, User, Calendar, ChatDotRound, UserFilled, 
  Setting, SwitchButton, TrendCharts 
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 菜单项
const menuItems = computed(() => {
  const baseItems = [
    {
      name: 'DashboardHome',
      path: '/dashboard',
      label: '概览',
      icon: 'TrendCharts'
    },
    {
      name: 'Profile',
      path: '/dashboard/profile',
      label: '个人信息',
      icon: 'User'
    },
    {
      name: 'Appointments',
      path: '/dashboard/appointments',
      label: '我的预约',
      icon: 'Calendar'
    },
    {
      name: 'Sessions',
      path: '/dashboard/sessions',
      label: '咨询记录',
      icon: 'ChatDotRound'
    }
  ]
  
  // 如果用户不是咨询师，添加申请成为咨询师的选项
  if (!userStore.isCounselor) {
    baseItems.push({
      name: 'CounselorApply',
      path: '/dashboard/counselor-apply',
      label: '申请成为咨询师',
      icon: 'UserFilled'
    })
  } else {
    // 如果是咨询师，添加咨询师中心
    baseItems.push({
      name: 'CounselorCenter',
      path: '/dashboard/counselor-center',
      label: '咨询师中心',
      icon: 'Setting'
    })
  }
  
  return baseItems
})

// 当前页面标题
const currentPageTitle = computed(() => {
  const currentItem = menuItems.value.find(item => item.name === route.name)
  return currentItem?.label || '个人中心'
})

// 返回首页
const goHome = () => {
  router.push('/')
}

// 退出登录
const handleLogout = async () => {
  await userStore.logoutUser()
  router.push('/')
}
</script>

<style lang="scss" scoped>
.dashboard {
  display: flex;
  min-height: 100vh;
  background: var(--bg-light);
}

.sidebar {
  width: 280px;
  background: var(--bg-white);
  border-right: 1px solid var(--border-light);
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  z-index: 10;
}

.sidebar-header {
  padding: 30px 20px;
  border-bottom: 1px solid var(--border-light);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-details {
  h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 5px;
  }
  
  p {
    color: var(--text-secondary);
    font-size: 0.9rem;
  }
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 25px;
  color: var(--text-regular);
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  
  &:hover {
    background: var(--bg-lighter);
    color: var(--primary-color);
  }
  
  &.active {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
    font-weight: 500;
  }
  
  .el-icon {
    font-size: 1.2rem;
  }
  
  span {
    font-size: 0.95rem;
  }
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid var(--border-light);
}

.logout-btn {
  width: 100%;
  justify-content: flex-start;
  gap: 10px;
  color: var(--text-secondary);
  
  &:hover {
    color: var(--danger-color);
  }
}

.main-content {
  flex: 1;
  margin-left: 280px;
  display: flex;
  flex-direction: column;
}

.content-header {
  background: var(--bg-white);
  padding: 20px 30px;
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 5;
}

.header-left {
  .el-breadcrumb {
    font-size: 0.9rem;
  }
}

.header-right {
  .el-button {
    color: var(--text-secondary);
    
    &:hover {
      color: var(--primary-color);
    }
  }
}

.content-body {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
}

// 响应式设计
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    position: fixed;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    
    &.mobile-open {
      transform: translateX(0);
    }
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .content-header {
    padding: 15px 20px;
  }
  
  .content-body {
    padding: 20px;
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .sidebar {
    background: #1a1a1a;
    border-right-color: #333;
  }
  
  .sidebar-header {
    border-bottom-color: #333;
  }
  
  .sidebar-footer {
    border-top-color: #333;
  }
  
  .content-header {
    background: #1a1a1a;
    border-bottom-color: #333;
  }
}
</style>
