package com.psychology.platform.service.impl;

import com.psychology.platform.entity.Counselor;
import com.psychology.platform.entity.User;
import com.psychology.platform.exception.BusinessException;
import com.psychology.platform.exception.ResourceNotFoundException;
import com.psychology.platform.repository.CounselorRepository;
import com.psychology.platform.repository.UserRepository;
import com.psychology.platform.service.CounselorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 咨询师服务实现类
 * 实现咨询师相关的业务逻辑
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 */
@Service
@Transactional
public class CounselorServiceImpl implements CounselorService {

    @Autowired
    private CounselorRepository counselorRepository;

    @Autowired
    private UserRepository userRepository;

    @Override
    public Counselor applyCounselor(Long userId, String licenseNumber, 
                                  Counselor.Specialization specialization, 
                                  String professionalSummary) {
        // 检查用户是否存在
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("用户", "id", userId));

        // 检查用户是否已经是咨询师
        if (counselorRepository.findByUserId(userId).isPresent()) {
            throw new BusinessException("用户已经是咨询师");
        }

        // 检查执业证书编号是否已存在
        if (counselorRepository.existsByLicenseNumber(licenseNumber)) {
            throw new BusinessException("执业证书编号已存在");
        }

        // 创建咨询师申请
        Counselor counselor = new Counselor();
        counselor.setUser(user);
        counselor.setLicenseNumber(licenseNumber);
        counselor.setSpecialization(specialization);
        counselor.setProfessionalSummary(professionalSummary);
        counselor.setStatus(Counselor.CounselorStatus.PENDING);

        return counselorRepository.save(counselor);
    }

    @Override
    public void reviewCounselorApplication(Long counselorId, boolean approved, String reason) {
        Counselor counselor = counselorRepository.findById(counselorId)
                .orElseThrow(() -> new ResourceNotFoundException("咨询师", "id", counselorId));

        if (counselor.getStatus() != Counselor.CounselorStatus.PENDING) {
            throw new BusinessException("只能审核待审核状态的申请");
        }

        if (approved) {
            counselor.setStatus(Counselor.CounselorStatus.APPROVED);
            counselor.setVerifiedAt(LocalDateTime.now());
            counselor.setAvailableForBooking(true);
        } else {
            counselor.setStatus(Counselor.CounselorStatus.REJECTED);
            counselor.setRejectionReason(reason);
        }

        counselorRepository.save(counselor);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Counselor> findById(Long id) {
        return counselorRepository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Counselor> findByUserId(Long userId) {
        return counselorRepository.findByUserId(userId);
    }

    @Override
    public Counselor updateCounselor(Long counselorId, Counselor counselorDetails) {
        Counselor counselor = counselorRepository.findById(counselorId)
                .orElseThrow(() -> new ResourceNotFoundException("咨询师", "id", counselorId));

        // 更新咨询师信息
        if (counselorDetails.getHourlyRate() != null) {
            counselor.setHourlyRate(counselorDetails.getHourlyRate());
        }
        if (counselorDetails.getProfessionalSummary() != null) {
            counselor.setProfessionalSummary(counselorDetails.getProfessionalSummary());
        }
        if (counselorDetails.getEducation() != null) {
            counselor.setEducation(counselorDetails.getEducation());
        }
        if (counselorDetails.getYearsOfExperience() != null) {
            counselor.setYearsOfExperience(counselorDetails.getYearsOfExperience());
        }

        return counselorRepository.save(counselor);
    }

    @Override
    public void setAvailableForBooking(Long counselorId, boolean available) {
        Counselor counselor = counselorRepository.findById(counselorId)
                .orElseThrow(() -> new ResourceNotFoundException("咨询师", "id", counselorId));

        if (counselor.getStatus() != Counselor.CounselorStatus.APPROVED) {
            throw new BusinessException("只有已认证的咨询师才能设置预约状态");
        }

        counselor.setAvailableForBooking(available);
        counselorRepository.save(counselor);
    }

    @Override
    public void updateHourlyRate(Long counselorId, BigDecimal hourlyRate) {
        Counselor counselor = counselorRepository.findById(counselorId)
                .orElseThrow(() -> new ResourceNotFoundException("咨询师", "id", counselorId));

        counselor.setHourlyRate(hourlyRate);
        counselorRepository.save(counselor);
    }

    @Override
    public void suspendCounselor(Long counselorId) {
        Counselor counselor = counselorRepository.findById(counselorId)
                .orElseThrow(() -> new ResourceNotFoundException("咨询师", "id", counselorId));

        counselor.setStatus(Counselor.CounselorStatus.SUSPENDED);
        counselor.setAvailableForBooking(false);
        counselorRepository.save(counselor);
    }

    @Override
    public void resumeCounselor(Long counselorId) {
        Counselor counselor = counselorRepository.findById(counselorId)
                .orElseThrow(() -> new ResourceNotFoundException("咨询师", "id", counselorId));

        if (counselor.getStatus() == Counselor.CounselorStatus.SUSPENDED) {
            counselor.setStatus(Counselor.CounselorStatus.APPROVED);
            counselor.setAvailableForBooking(true);
            counselorRepository.save(counselor);
        } else {
            throw new BusinessException("只能恢复已暂停的咨询师");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Counselor> findAllCounselors(Pageable pageable) {
        return counselorRepository.findAll(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Counselor> findCounselorsByStatus(Counselor.CounselorStatus status, Pageable pageable) {
        return counselorRepository.findByStatus(status, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Counselor> findAvailableCounselors(Pageable pageable) {
        return counselorRepository.findAvailableCounselors(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Counselor> findCounselorsBySpecialization(Counselor.Specialization specialization, Pageable pageable) {
        return counselorRepository.findBySpecialization(specialization, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Counselor> searchCounselors(Counselor.Specialization specialization,
                                          BigDecimal minRate, BigDecimal maxRate,
                                          BigDecimal minRating, Integer minYears,
                                          Pageable pageable) {
        return counselorRepository.searchCounselors(specialization, minRate, maxRate, minRating, minYears, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Counselor> getPopularCounselors(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return counselorRepository.findMostPopularCounselors(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Counselor> getTopRatedCounselors(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return counselorRepository.findTopRatedCounselors(pageable);
    }

    @Override
    public void updateCounselorRating(Long counselorId) {
        Counselor counselor = counselorRepository.findById(counselorId)
                .orElseThrow(() -> new ResourceNotFoundException("咨询师", "id", counselorId));

        // 重新计算平均评分
        // 这里应该从咨询会话中计算平均评分
        // 暂时使用Repository中的方法
        // Double avgRating = counselingSessionRepository.calculateAverageRatingByCounselorId(counselorId);
        // if (avgRating != null) {
        //     counselor.setRating(BigDecimal.valueOf(avgRating));
        //     counselorRepository.save(counselor);
        // }
    }

    @Override
    public void incrementSessionCount(Long counselorId) {
        Counselor counselor = counselorRepository.findById(counselorId)
                .orElseThrow(() -> new ResourceNotFoundException("咨询师", "id", counselorId));

        counselor.setTotalSessions(counselor.getTotalSessions() + 1);
        counselorRepository.save(counselor);
    }

    @Override
    @Transactional(readOnly = true)
    public long countTotalCounselors() {
        return counselorRepository.count();
    }

    @Override
    @Transactional(readOnly = true)
    public long countCounselorsByStatus(Counselor.CounselorStatus status) {
        return counselorRepository.countByStatus(status);
    }

    @Override
    @Transactional(readOnly = true)
    public long countCounselorsBySpecialization(Counselor.Specialization specialization) {
        return counselorRepository.countBySpecialization(specialization);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isLicenseNumberExists(String licenseNumber) {
        return counselorRepository.existsByLicenseNumber(licenseNumber);
    }
}
