import request from './request'

// 获取咨询师列表
export const getCounselors = (params) => {
  return request({
    url: '/counselors',
    method: 'get',
    params
  })
}

// 获取可预约的咨询师
export const getAvailableCounselors = (params) => {
  return request({
    url: '/counselors/available',
    method: 'get',
    params
  })
}

// 搜索咨询师
export const searchCounselors = (params) => {
  return request({
    url: '/counselors/search',
    method: 'get',
    params
  })
}

// 获取热门咨询师
export const getPopularCounselors = (limit = 10) => {
  return request({
    url: '/counselors/popular',
    method: 'get',
    params: { limit }
  })
}

// 获取评分最高的咨询师
export const getTopRatedCounselors = (limit = 10) => {
  return request({
    url: '/counselors/top-rated',
    method: 'get',
    params: { limit }
  })
}

// 获取咨询师详情
export const getCounselorDetail = (id) => {
  return request({
    url: `/counselors/${id}`,
    method: 'get'
  })
}

// 根据用户ID获取咨询师信息
export const getCounselorByUserId = (userId) => {
  return request({
    url: `/counselors/user/${userId}`,
    method: 'get'
  })
}

// 申请成为咨询师
export const applyCounselor = (data) => {
  return request({
    url: '/counselors/apply',
    method: 'post',
    data
  })
}

// 更新咨询师信息
export const updateCounselor = (id, data) => {
  return request({
    url: `/counselors/${id}`,
    method: 'put',
    data
  })
}

// 设置可预约状态
export const setAvailability = (id, available) => {
  return request({
    url: `/counselors/${id}/availability`,
    method: 'post',
    params: { available }
  })
}

// 更新咨询费率
export const updateHourlyRate = (id, hourlyRate) => {
  return request({
    url: `/counselors/${id}/hourly-rate`,
    method: 'put',
    params: { hourlyRate }
  })
}

// 审核咨询师申请（管理员）
export const reviewCounselorApplication = (id, data) => {
  return request({
    url: `/counselors/${id}/review`,
    method: 'post',
    data
  })
}

// 暂停咨询师服务（管理员）
export const suspendCounselor = (id) => {
  return request({
    url: `/counselors/${id}/suspend`,
    method: 'post'
  })
}

// 恢复咨询师服务（管理员）
export const resumeCounselor = (id) => {
  return request({
    url: `/counselors/${id}/resume`,
    method: 'post'
  })
}

// 检查执业证书编号是否存在
export const checkLicenseExists = (licenseNumber) => {
  return request({
    url: '/counselors/check/license',
    method: 'get',
    params: { licenseNumber }
  })
}

// 获取咨询师统计信息
export const getCounselorStats = () => {
  return request({
    url: '/counselors/stats/total',
    method: 'get'
  })
}
