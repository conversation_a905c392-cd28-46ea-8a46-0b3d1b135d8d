<template>
  <div class="auth-page">
    <div class="auth-container">
      <div class="auth-card card">
        <div class="auth-header">
          <h2>欢迎回来</h2>
          <p>登录您的账户继续使用服务</p>
        </div>
        
        <el-form 
          ref="loginFormRef"
          :model="loginForm" 
          :rules="loginRules"
          class="auth-form"
          @submit.prevent="handleLogin"
        >
          <el-form-item prop="emailOrUsername">
            <el-input
              v-model="loginForm.emailOrUsername"
              placeholder="请输入邮箱或用户名"
              size="large"
              :prefix-icon="User"
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              size="large"
              :prefix-icon="Lock"
              show-password
            />
          </el-form-item>
          
          <div class="auth-options">
            <el-checkbox v-model="rememberMe">记住我</el-checkbox>
            <el-button type="text" @click="showForgotPassword">忘记密码？</el-button>
          </div>
          
          <el-form-item>
            <el-button 
              type="primary" 
              size="large" 
              :loading="loading"
              @click="handleLogin"
              class="auth-button"
            >
              登录
            </el-button>
          </el-form-item>
        </el-form>
        
        <div class="auth-footer">
          <p>还没有账户？ 
            <router-link to="/register" class="auth-link">立即注册</router-link>
          </p>
        </div>
      </div>
      
      <div class="auth-side">
        <div class="side-content">
          <h3>专业心理咨询服务</h3>
          <p>连接专业咨询师，获得个性化心理健康支持</p>
          <div class="side-features">
            <div class="side-feature">
              <el-icon><Shield /></el-icon>
              <span>隐私保护</span>
            </div>
            <div class="side-feature">
              <el-icon><Star /></el-icon>
              <span>专业认证</span>
            </div>
            <div class="side-feature">
              <el-icon><Clock /></el-icon>
              <span>灵活预约</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 忘记密码对话框 -->
    <el-dialog 
      v-model="forgotPasswordVisible" 
      title="重置密码" 
      width="400px"
    >
      <el-form :model="forgotPasswordForm" :rules="forgotPasswordRules" ref="forgotPasswordFormRef">
        <el-form-item prop="email">
          <el-input
            v-model="forgotPasswordForm.email"
            placeholder="请输入注册邮箱"
            :prefix-icon="Message"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="forgotPasswordVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          :loading="resetLoading"
          @click="handleResetPassword"
        >
          发送重置邮件
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { resetPassword } from '@/api/auth'
import { ElMessage } from 'element-plus'
import { User, Lock, Shield, Star, Clock, Message } from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const resetLoading = ref(false)
const rememberMe = ref(false)
const forgotPasswordVisible = ref(false)

const loginFormRef = ref()
const forgotPasswordFormRef = ref()

// 登录表单
const loginForm = reactive({
  emailOrUsername: '',
  password: ''
})

// 忘记密码表单
const forgotPasswordForm = reactive({
  email: ''
})

// 表单验证规则
const loginRules = {
  emailOrUsername: [
    { required: true, message: '请输入邮箱或用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

const forgotPasswordRules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    loading.value = true
    
    const success = await userStore.loginUser(loginForm)
    if (success) {
      router.push('/dashboard')
    }
  } catch (error) {
    console.error('Login validation failed:', error)
  } finally {
    loading.value = false
  }
}

// 显示忘记密码对话框
const showForgotPassword = () => {
  forgotPasswordVisible.value = true
}

// 处理重置密码
const handleResetPassword = async () => {
  if (!forgotPasswordFormRef.value) return
  
  try {
    await forgotPasswordFormRef.value.validate()
    resetLoading.value = true
    
    await resetPassword(forgotPasswordForm)
    ElMessage.success('重置密码邮件已发送，请查收邮箱')
    forgotPasswordVisible.value = false
    forgotPasswordForm.email = ''
  } catch (error) {
    console.error('Reset password failed:', error)
  } finally {
    resetLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.auth-container {
  display: flex;
  max-width: 900px;
  width: 100%;
  min-height: 600px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.auth-card {
  flex: 1;
  padding: 60px 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: var(--bg-white);
  border-radius: 0;
  box-shadow: none;
}

.auth-header {
  text-align: center;
  margin-bottom: 40px;
}

.auth-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 10px;
}

.auth-header p {
  color: var(--text-secondary);
  font-size: 1rem;
}

.auth-form {
  .el-form-item {
    margin-bottom: 25px;
  }
}

.auth-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.auth-button {
  width: 100%;
  height: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 12px;
}

.auth-footer {
  text-align: center;
  margin-top: 30px;
  
  p {
    color: var(--text-secondary);
  }
}

.auth-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  
  &:hover {
    text-decoration: underline;
  }
}

.auth-side {
  flex: 1;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  }
}

.side-content {
  text-align: center;
  padding: 40px;
  position: relative;
  z-index: 1;
}

.side-content h3 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.side-content p {
  font-size: 1.1rem;
  margin-bottom: 40px;
  opacity: 0.9;
  line-height: 1.6;
}

.side-features {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.side-feature {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 1rem;
  
  .el-icon {
    font-size: 1.5rem;
    opacity: 0.8;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .auth-container {
    flex-direction: column;
    max-width: 400px;
  }
  
  .auth-side {
    order: -1;
    min-height: 200px;
  }
  
  .auth-card {
    padding: 40px 30px;
  }
  
  .side-content {
    padding: 20px;
  }
  
  .side-content h3 {
    font-size: 1.5rem;
  }
  
  .side-features {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }
}
</style>
