-- 心理咨询服务平台 - 示例数据
-- 用于开发和测试的示例数据

USE psychology_platform;

-- 清空现有数据（谨慎使用）
-- SET FOREIGN_KEY_CHECKS = 0;
-- TRUNCATE TABLE operation_logs;
-- TRUNCATE TABLE counseling_sessions;
-- TRUNCATE TABLE appointments;
-- TRUNCATE TABLE verification_codes;
-- TRUNCATE TABLE counselors;
-- TRUNCATE TABLE users;
-- SET FOREIGN_KEY_CHECKS = 1;

-- ================================
-- 插入示例用户数据
-- ================================

INSERT INTO users (username, email, password, real_name, phone, gender, birth_date, bio, status, email_verified, phone_verified) VALUES
-- 普通用户
('user001', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkYjjOgqOzypfI8Fzu', '张三', '13800138001', 'MALE', '1990-05-15', '希望通过心理咨询改善生活质量', 'ACTIVE', TRUE, TRUE),
('user002', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkYjjOgqOzypfI8Fzu', '李四', '13800138002', 'FEMALE', '1985-08-22', '关注心理健康，寻求专业帮助', 'ACTIVE', TRUE, FALSE),
('user003', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkYjjOgqOzypfI8Fzu', '王五', '13800138003', 'MALE', '1992-12-03', '工作压力大，需要心理疏导', 'ACTIVE', TRUE, TRUE),
('user004', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkYjjOgqOzypfI8Fzu', '赵六', '13800138004', 'FEMALE', '1988-03-18', '情感问题困扰，寻求专业建议', 'ACTIVE', FALSE, FALSE),
('user005', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkYjjOgqOzypfI8Fzu', '钱七', '13800138005', 'OTHER', '1995-07-09', '焦虑症患者，正在寻求治疗', 'ACTIVE', TRUE, TRUE),

-- 咨询师用户
('counselor001', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkYjjOgqOzypfI8Fzu', '陈心理', '13900139001', 'FEMALE', '1980-04-12', '专业心理咨询师，擅长焦虑和抑郁治疗', 'ACTIVE', TRUE, TRUE),
('counselor002', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkYjjOgqOzypfI8Fzu', '刘医生', '13900139002', 'MALE', '1975-11-28', '资深心理治疗师，专注家庭治疗', 'ACTIVE', TRUE, TRUE),
('counselor003', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkYjjOgqOzypfI8Fzu', '周咨询', '13900139003', 'FEMALE', '1983-09-14', '儿童心理专家，温和耐心', 'ACTIVE', TRUE, TRUE),
('counselor004', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkYjjOgqOzypfI8Fzu', '吴老师', '13900139004', 'MALE', '1978-06-25', '职业规划和压力管理专家', 'ACTIVE', TRUE, TRUE),
('counselor005', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkYjjOgqOzypfI8Fzu', '郑博士', '13900139005', 'FEMALE', '1982-01-30', '创伤治疗和情感咨询专家', 'ACTIVE', TRUE, TRUE);

-- ================================
-- 插入咨询师数据
-- ================================

INSERT INTO counselors (user_id, license_number, specialization, professional_summary, education, years_of_experience, hourly_rate, status, available_for_booking, rating, total_sessions, verified_at) VALUES
(6, 'PSY20180001', 'ANXIETY_DISORDERS', '专注于焦虑障碍和抑郁症的认知行为治疗，拥有丰富的临床经验。采用循证治疗方法，帮助来访者建立健康的思维模式。', '北京大学心理学硕士', 8, 300.00, 'APPROVED', TRUE, 4.8, 156, '2023-01-15 10:30:00'),
(7, 'PSY20180002', 'FAMILY_THERAPY', '家庭系统治疗专家，擅长处理家庭关系冲突、亲子关系问题。注重家庭动力学分析，促进家庭成员间的理解与沟通。', '清华大学应用心理学博士', 12, 450.00, 'APPROVED', TRUE, 4.9, 203, '2023-01-20 14:20:00'),
(8, 'PSY20180003', 'CHILD_PSYCHOLOGY', '儿童青少年心理专家，专业处理学习困难、行为问题、情绪调节等。善于与儿童建立信任关系，采用游戏治疗等方法。', '华东师范大学发展心理学硕士', 6, 280.00, 'APPROVED', TRUE, 4.7, 89, '2023-02-01 09:15:00'),
(9, 'PSY20180004', 'CAREER_COUNSELING', '职业发展和压力管理专家，帮助职场人士解决工作压力、职业规划、人际关系等问题。结合积极心理学理念。', '中科院心理研究所硕士', 10, 350.00, 'APPROVED', TRUE, 4.6, 134, '2023-02-10 16:45:00'),
(10, 'PSY20180005', 'TRAUMA_THERAPY', '创伤后应激障碍治疗专家，擅长EMDR、认知处理治疗等循证方法。对情感创伤、关系创伤有深入研究。', '复旦大学临床心理学博士', 9, 400.00, 'APPROVED', TRUE, 4.9, 178, '2023-02-15 11:30:00');

-- ================================
-- 插入预约数据
-- ================================

INSERT INTO appointments (user_id, counselor_id, scheduled_time, actual_start_time, actual_end_time, fee, notes, status, confirmed_at) VALUES
-- 已完成的预约
(1, 1, '2024-01-15 10:00:00', '2024-01-15 10:05:00', '2024-01-15 11:00:00', 300.00, '第一次咨询，主要了解焦虑症状', 'COMPLETED', '2024-01-14 15:30:00'),
(1, 1, '2024-01-22 10:00:00', '2024-01-22 10:00:00', '2024-01-22 11:00:00', 300.00, '继续焦虑治疗，讨论应对策略', 'COMPLETED', '2024-01-21 16:20:00'),
(2, 2, '2024-01-18 14:00:00', '2024-01-18 14:00:00', '2024-01-18 15:00:00', 450.00, '家庭关系咨询，夫妻沟通问题', 'COMPLETED', '2024-01-17 10:15:00'),
(3, 4, '2024-01-20 16:00:00', '2024-01-20 16:00:00', '2024-01-20 17:00:00', 350.00, '工作压力管理，职业规划咨询', 'COMPLETED', '2024-01-19 14:30:00'),
(4, 5, '2024-01-25 09:00:00', '2024-01-25 09:00:00', '2024-01-25 10:00:00', 400.00, '情感创伤处理，分手后心理调适', 'COMPLETED', '2024-01-24 11:45:00'),

-- 即将进行的预约
(1, 1, '2024-02-05 10:00:00', NULL, NULL, 300.00, '第三次咨询，评估治疗进展', 'CONFIRMED', '2024-02-04 09:30:00'),
(2, 2, '2024-02-06 14:00:00', NULL, NULL, 450.00, '家庭治疗第二次，全家参与', 'CONFIRMED', '2024-02-05 16:20:00'),
(5, 3, '2024-02-07 15:00:00', NULL, NULL, 280.00, '青少年心理咨询，学习压力问题', 'CONFIRMED', '2024-02-06 13:15:00'),

-- 待确认的预约
(3, 4, '2024-02-08 10:00:00', NULL, NULL, 350.00, '职场人际关系处理', 'PENDING', NULL),
(4, 5, '2024-02-09 11:00:00', NULL, NULL, 400.00, '创伤治疗后续咨询', 'PENDING', NULL);

-- ================================
-- 插入咨询会话数据
-- ================================

INSERT INTO counseling_sessions (appointment_id, start_time, end_time, session_notes, counselor_notes, user_feedback, user_rating, status, follow_up_required, follow_up_date) VALUES
(1, '2024-01-15 10:05:00', '2024-01-15 11:00:00', 
 '来访者主诉工作压力导致的焦虑症状，包括失眠、心悸、担忧等。通过初步评估，确定为广泛性焦虑障碍。制定了认知行为治疗计划。', 
 '来访者配合度良好，有强烈的治疗动机。建议进行系统的CBT治疗，预计需要8-12次咨询。', 
 '咨询师很专业，让我感到被理解和支持。期待下次咨询。', 
 5, 'COMPLETED', TRUE, '2024-01-22 10:00:00'),

(2, '2024-01-22 10:00:00', '2024-01-22 11:00:00',
 '继续CBT治疗，教授放松技巧和认知重构方法。来访者报告焦虑症状有所缓解，睡眠质量改善。',
 '治疗进展良好，来访者掌握了基本的应对技巧。继续强化练习，逐步减少回避行为。',
 '学到了很多实用的方法，焦虑确实有所减轻。会继续练习老师教的技巧。',
 5, 'COMPLETED', TRUE, '2024-02-05 10:00:00'),

(3, '2024-01-18 14:00:00', '2024-01-18 15:00:00',
 '夫妻双方就沟通模式进行了深入探讨。识别了冲突的根源和各自的需求。制定了改善沟通的具体策略。',
 '夫妻关系紧张主要源于沟通不良和期望不匹配。需要进一步的家庭治疗来改善关系动力。',
 '第一次和爱人一起咨询，虽然有些紧张，但感觉很有帮助。开始理解对方的想法。',
 4, 'COMPLETED', TRUE, '2024-02-06 14:00:00'),

(4, '2024-01-20 16:00:00', '2024-01-20 17:00:00',
 '探讨了工作压力的来源和应对方式。进行了职业价值观澄清，制定了短期和长期的职业目标。',
 '来访者对职业发展有清晰的认识，主要问题是压力管理和时间管理。提供了相关技巧和资源。',
 '对自己的职业方向更清楚了，也学会了一些减压方法。感谢老师的指导。',
 4, 'COMPLETED', FALSE, NULL),

(5, '2024-01-25 09:00:00', '2024-01-25 10:00:00',
 '处理分手后的情感创伤，使用EMDR技术处理创伤记忆。来访者情绪稳定，开始接受现实。',
 '创伤反应明显减轻，来访者展现出良好的恢复能力。建议继续巩固治疗效果。',
 '虽然过程有些痛苦，但感觉心里的重担轻了很多。谢谢老师的耐心陪伴。',
 5, 'COMPLETED', TRUE, '2024-02-09 11:00:00');

-- ================================
-- 插入验证码数据（示例）
-- ================================

INSERT INTO verification_codes (user_id, email, code, type, used, expires_at) VALUES
(1, '<EMAIL>', '123456', 'EMAIL_VERIFICATION', TRUE, '2024-01-01 10:10:00'),
(2, '<EMAIL>', '789012', 'EMAIL_VERIFICATION', TRUE, '2024-01-02 11:15:00'),
(NULL, '<EMAIL>', '456789', 'EMAIL_VERIFICATION', FALSE, '2024-02-10 15:30:00');

-- ================================
-- 插入操作日志数据（示例）
-- ================================

INSERT INTO operation_logs (user_id, operation_type, operation_desc, ip_address, user_agent, request_url, request_method, response_status, execution_time) VALUES
(1, 'USER_LOGIN', '用户登录', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', '/api/users/login', 'POST', 200, 150),
(1, 'APPOINTMENT_CREATE', '创建预约', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', '/api/appointments', 'POST', 201, 200),
(6, 'COUNSELOR_LOGIN', '咨询师登录', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)', '/api/users/login', 'POST', 200, 120),
(6, 'APPOINTMENT_CONFIRM', '确认预约', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)', '/api/appointments/1/confirm', 'POST', 200, 80),
(1, 'SESSION_FEEDBACK', '提交会话反馈', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', '/api/sessions/1/feedback', 'POST', 200, 100);

-- ================================
-- 更新统计数据
-- ================================

-- 更新咨询师的评分和会话数量
UPDATE counselors c SET 
    rating = (
        SELECT ROUND(AVG(cs.user_rating), 2) 
        FROM counseling_sessions cs 
        JOIN appointments a ON cs.appointment_id = a.id 
        WHERE a.counselor_id = c.id AND cs.user_rating IS NOT NULL
    ),
    total_sessions = (
        SELECT COUNT(*) 
        FROM counseling_sessions cs 
        JOIN appointments a ON cs.appointment_id = a.id 
        WHERE a.counselor_id = c.id AND cs.status = 'COMPLETED'
    )
WHERE c.id IN (1, 2, 3, 4, 5);

-- 更新用户最后登录时间
UPDATE users SET last_login_time = NOW() WHERE id IN (1, 2, 3, 4, 5, 6, 7, 8, 9, 10);
