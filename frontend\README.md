# 心理咨询服务平台 - 前端

基于 Vue 3 + Element Plus 构建的现代化心理咨询服务平台前端应用。

## 🚀 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **Vite** - 下一代前端构建工具
- **Element Plus** - 基于 Vue 3 的组件库
- **Vue Router** - 官方路由管理器
- **Pinia** - Vue 状态管理库
- **Axios** - HTTP 客户端
- **Sass** - CSS 预处理器
- **Day.js** - 轻量级日期处理库

## 📁 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API 接口
│   │   ├── request.js     # Axios 配置
│   │   ├── auth.js        # 认证相关 API
│   │   ├── counselor.js   # 咨询师相关 API
│   │   ├── appointment.js # 预约相关 API
│   │   └── session.js     # 会话相关 API
│   ├── components/        # 公共组件
│   ├── router/           # 路由配置
│   ├── stores/           # Pinia 状态管理
│   ├── styles/           # 全局样式
│   ├── views/            # 页面组件
│   │   ├── Home.vue      # 首页
│   │   ├── auth/         # 认证页面
│   │   ├── counselors/   # 咨询师相关页面
│   │   ├── dashboard/    # 个人中心
│   │   └── error/        # 错误页面
│   ├── App.vue           # 根组件
│   └── main.js           # 入口文件
├── index.html            # HTML 模板
├── package.json          # 依赖配置
├── vite.config.js        # Vite 配置
└── README.md             # 项目说明
```

## 🎨 功能特性

### 🏠 首页
- 响应式设计的现代化首页
- 热门咨询师展示
- 服务特色介绍
- 用户导航和快速注册

### 🔐 用户认证
- 用户注册/登录
- 表单验证和错误处理
- JWT Token 管理
- 密码重置功能

### 👨‍⚕️ 咨询师模块
- 咨询师列表和搜索
- 多条件筛选（专业、价格、评分等）
- 咨询师详情页面
- 在线预约功能

### 🏠 个人中心
- 用户信息管理
- 预约记录查看
- 咨询会话历史
- 咨询师申请
- 数据统计展示

### 📱 响应式设计
- 移动端适配
- 平板端优化
- 桌面端完整体验

## 🛠️ 开发指南

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
cd frontend
npm install
```

### 开发模式

```bash
npm run dev
```

访问 http://localhost:5173

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 🔧 配置说明

### API 代理配置

在 `vite.config.js` 中配置了 API 代理：

```javascript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8080',
      changeOrigin: true
    }
  }
}
```

### 环境变量

可以创建 `.env` 文件来配置环境变量：

```bash
# API 基础地址
VITE_API_BASE_URL=http://localhost:8080/api

# 应用标题
VITE_APP_TITLE=心理咨询服务平台
```

## 🎯 页面路由

| 路径 | 页面 | 说明 |
|------|------|------|
| `/` | 首页 | 平台介绍和导航 |
| `/login` | 登录 | 用户登录 |
| `/register` | 注册 | 用户注册 |
| `/counselors` | 咨询师列表 | 浏览和搜索咨询师 |
| `/counselors/:id` | 咨询师详情 | 查看咨询师详细信息 |
| `/dashboard` | 个人中心 | 用户个人信息管理 |
| `/dashboard/profile` | 个人信息 | 编辑个人资料 |
| `/dashboard/appointments` | 我的预约 | 预约管理 |
| `/dashboard/sessions` | 咨询记录 | 会话历史 |
| `/booking/:counselorId` | 预约咨询 | 创建新预约 |

## 🔒 权限控制

- **游客访问**：首页、咨询师列表、登录、注册
- **登录用户**：个人中心、预约功能、咨询记录
- **咨询师**：咨询师中心、会话管理

## 🎨 UI 设计

### 设计原则
- **简约大气**：清晰的视觉层次和简洁的界面
- **专业可信**：体现心理咨询的专业性
- **温暖友好**：使用温和的色彩和圆润的设计
- **易用性**：直观的交互和清晰的信息架构

### 色彩方案
- **主色调**：#667eea (渐变蓝)
- **辅助色**：#764ba2 (渐变紫)
- **成功色**：#67c23a
- **警告色**：#e6a23c
- **危险色**：#f56c6c

### 组件风格
- 圆角设计 (8-12px)
- 柔和阴影效果
- 渐变背景
- 微交互动画

## 📱 响应式断点

- **移动端**：< 768px
- **平板端**：768px - 1024px
- **桌面端**：> 1024px

## 🚀 部署说明

### 构建优化
- 代码分割和懒加载
- 静态资源压缩
- Tree-shaking 优化
- 浏览器缓存策略

### 部署步骤
1. 构建生产版本：`npm run build`
2. 将 `dist` 目录部署到 Web 服务器
3. 配置 Nginx 或 Apache 的路由重写
4. 设置 HTTPS 和安全头

## 🤝 开发规范

### 代码风格
- 使用 ESLint + Prettier
- Vue 3 Composition API
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case

### Git 提交规范
- feat: 新功能
- fix: 修复问题
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 📞 技术支持

如有问题或建议，请联系开发团队。
