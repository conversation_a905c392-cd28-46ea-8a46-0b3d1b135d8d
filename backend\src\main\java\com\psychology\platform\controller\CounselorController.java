package com.psychology.platform.controller;

import com.psychology.platform.entity.Counselor;
import com.psychology.platform.service.CounselorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 咨询师控制器
 * 处理咨询师相关的HTTP请求
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/counselors")
@Tag(name = "咨询师管理", description = "咨询师申请、审核、信息管理等接口")
public class CounselorController {

    @Autowired
    private CounselorService counselorService;

    @PostMapping("/apply")
    @Operation(summary = "申请成为咨询师", description = "用户申请成为认证咨询师")
    public ResponseEntity<Counselor> applyCounselor(@Valid @RequestBody CounselorApplicationRequest request) {
        Counselor counselor = counselorService.applyCounselor(
            request.getUserId(),
            request.getLicenseNumber(),
            request.getSpecialization(),
            request.getProfessionalSummary()
        );
        return ResponseEntity.ok(counselor);
    }

    @PostMapping("/{id}/review")
    @Operation(summary = "审核咨询师申请", description = "管理员审核咨询师申请")
    public ResponseEntity<Void> reviewApplication(@Parameter(description = "咨询师ID") @PathVariable Long id,
                                                @Valid @RequestBody ReviewRequest request) {
        counselorService.reviewCounselorApplication(id, request.isApproved(), request.getReason());
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取咨询师信息", description = "根据咨询师ID获取详细信息")
    public ResponseEntity<Counselor> getCounselorById(@Parameter(description = "咨询师ID") @PathVariable Long id) {
        Optional<Counselor> counselor = counselorService.findById(id);
        return counselor.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "根据用户ID获取咨询师信息", description = "查找用户对应的咨询师信息")
    public ResponseEntity<Counselor> getCounselorByUserId(@Parameter(description = "用户ID") @PathVariable Long userId) {
        Optional<Counselor> counselor = counselorService.findByUserId(userId);
        return counselor.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新咨询师信息", description = "更新咨询师的专业信息")
    public ResponseEntity<Counselor> updateCounselor(@Parameter(description = "咨询师ID") @PathVariable Long id,
                                                   @Valid @RequestBody CounselorUpdateRequest request) {
        Counselor counselorDetails = new Counselor();
        counselorDetails.setHourlyRate(request.getHourlyRate());
        counselorDetails.setProfessionalSummary(request.getProfessionalSummary());
        counselorDetails.setEducation(request.getEducation());
        counselorDetails.setYearsOfExperience(request.getYearsOfExperience());

        Counselor updatedCounselor = counselorService.updateCounselor(id, counselorDetails);
        return ResponseEntity.ok(updatedCounselor);
    }

    @PostMapping("/{id}/availability")
    @Operation(summary = "设置可预约状态", description = "咨询师设置是否接受预约")
    public ResponseEntity<Void> setAvailability(@Parameter(description = "咨询师ID") @PathVariable Long id,
                                              @RequestParam boolean available) {
        counselorService.setAvailableForBooking(id, available);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/hourly-rate")
    @Operation(summary = "更新咨询费率", description = "咨询师更新每小时咨询费用")
    public ResponseEntity<Void> updateHourlyRate(@Parameter(description = "咨询师ID") @PathVariable Long id,
                                                @RequestParam BigDecimal hourlyRate) {
        counselorService.updateHourlyRate(id, hourlyRate);
        return ResponseEntity.ok().build();
    }

    @GetMapping
    @Operation(summary = "分页查询咨询师", description = "分页获取咨询师列表")
    public ResponseEntity<Page<Counselor>> getAllCounselors(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Counselor> counselors = counselorService.findAllCounselors(pageable);
        return ResponseEntity.ok(counselors);
    }

    @GetMapping("/available")
    @Operation(summary = "查询可预约咨询师", description = "获取当前可接受预约的咨询师列表")
    public ResponseEntity<Page<Counselor>> getAvailableCounselors(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Counselor> counselors = counselorService.findAvailableCounselors(pageable);
        return ResponseEntity.ok(counselors);
    }

    @GetMapping("/search")
    @Operation(summary = "搜索咨询师", description = "根据条件搜索咨询师")
    public ResponseEntity<Page<Counselor>> searchCounselors(
            @Parameter(description = "专业领域") @RequestParam(required = false) Counselor.Specialization specialization,
            @Parameter(description = "最低费率") @RequestParam(required = false) BigDecimal minRate,
            @Parameter(description = "最高费率") @RequestParam(required = false) BigDecimal maxRate,
            @Parameter(description = "最低评分") @RequestParam(required = false) BigDecimal minRating,
            @Parameter(description = "最少经验年限") @RequestParam(required = false) Integer minYears,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<Counselor> counselors = counselorService.searchCounselors(
            specialization, minRate, maxRate, minRating, minYears, pageable);
        return ResponseEntity.ok(counselors);
    }

    @GetMapping("/popular")
    @Operation(summary = "获取热门咨询师", description = "获取最受欢迎的咨询师列表")
    public ResponseEntity<List<Counselor>> getPopularCounselors(
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") int limit) {
        List<Counselor> counselors = counselorService.getPopularCounselors(limit);
        return ResponseEntity.ok(counselors);
    }

    @GetMapping("/top-rated")
    @Operation(summary = "获取评分最高咨询师", description = "获取评分最高的咨询师列表")
    public ResponseEntity<List<Counselor>> getTopRatedCounselors(
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") int limit) {
        List<Counselor> counselors = counselorService.getTopRatedCounselors(limit);
        return ResponseEntity.ok(counselors);
    }

    @PostMapping("/{id}/suspend")
    @Operation(summary = "暂停咨询师服务", description = "管理员暂停咨询师服务")
    public ResponseEntity<Void> suspendCounselor(@Parameter(description = "咨询师ID") @PathVariable Long id) {
        counselorService.suspendCounselor(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/resume")
    @Operation(summary = "恢复咨询师服务", description = "管理员恢复咨询师服务")
    public ResponseEntity<Void> resumeCounselor(@Parameter(description = "咨询师ID") @PathVariable Long id) {
        counselorService.resumeCounselor(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/stats/total")
    @Operation(summary = "咨询师总数统计", description = "获取咨询师总数")
    public ResponseEntity<Long> getTotalCounselorCount() {
        long count = counselorService.countTotalCounselors();
        return ResponseEntity.ok(count);
    }

    @GetMapping("/check/license")
    @Operation(summary = "检查执业证书编号", description = "验证执业证书编号是否已存在")
    public ResponseEntity<Boolean> checkLicenseExists(@Parameter(description = "执业证书编号") @RequestParam String licenseNumber) {
        boolean exists = counselorService.isLicenseNumberExists(licenseNumber);
        return ResponseEntity.ok(exists);
    }

    // 请求对象定义
    public static class CounselorApplicationRequest {
        private Long userId;
        private String licenseNumber;
        private Counselor.Specialization specialization;
        private String professionalSummary;

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        public String getLicenseNumber() { return licenseNumber; }
        public void setLicenseNumber(String licenseNumber) { this.licenseNumber = licenseNumber; }
        public Counselor.Specialization getSpecialization() { return specialization; }
        public void setSpecialization(Counselor.Specialization specialization) { this.specialization = specialization; }
        public String getProfessionalSummary() { return professionalSummary; }
        public void setProfessionalSummary(String professionalSummary) { this.professionalSummary = professionalSummary; }
    }

    public static class ReviewRequest {
        private boolean approved;
        private String reason;

        // Getters and Setters
        public boolean isApproved() { return approved; }
        public void setApproved(boolean approved) { this.approved = approved; }
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }

    public static class CounselorUpdateRequest {
        private BigDecimal hourlyRate;
        private String professionalSummary;
        private String education;
        private Integer yearsOfExperience;

        // Getters and Setters
        public BigDecimal getHourlyRate() { return hourlyRate; }
        public void setHourlyRate(BigDecimal hourlyRate) { this.hourlyRate = hourlyRate; }
        public String getProfessionalSummary() { return professionalSummary; }
        public void setProfessionalSummary(String professionalSummary) { this.professionalSummary = professionalSummary; }
        public String getEducation() { return education; }
        public void setEducation(String education) { this.education = education; }
        public Integer getYearsOfExperience() { return yearsOfExperience; }
        public void setYearsOfExperience(Integer yearsOfExperience) { this.yearsOfExperience = yearsOfExperience; }
    }
}
