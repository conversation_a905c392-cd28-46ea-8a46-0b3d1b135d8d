-- 心理咨询服务平台 - 数据库视图
-- 为常用查询创建视图，简化业务逻辑

USE psychology_platform;

-- ================================
-- 用户相关视图
-- ================================

-- 用户基本信息视图（隐藏敏感信息）
CREATE OR REPLACE VIEW v_user_profile AS
SELECT 
    u.id,
    u.username,
    u.real_name,
    u.gender,
    u.birth_date,
    YEAR(CURDATE()) - YEAR(u.birth_date) - 
    (DATE_FORMAT(CURDATE(), '%m%d') < DATE_FORMAT(u.birth_date, '%m%d')) AS age,
    u.avatar_url,
    u.bio,
    u.status,
    u.email_verified,
    u.phone_verified,
    u.last_login_time,
    u.created_at,
    -- 检查是否为咨询师
    CASE WHEN c.id IS NOT NULL THEN TRUE ELSE FALSE END AS is_counselor,
    c.id AS counselor_id,
    c.status AS counselor_status
FROM users u
LEFT JOIN counselors c ON u.id = c.user_id;

-- ================================
-- 咨询师相关视图
-- ================================

-- 咨询师详细信息视图
CREATE OR REPLACE VIEW v_counselor_detail AS
SELECT 
    c.id,
    c.user_id,
    u.username,
    u.real_name,
    u.gender,
    u.avatar_url,
    u.bio AS user_bio,
    c.license_number,
    c.specialization,
    c.professional_summary,
    c.education,
    c.years_of_experience,
    c.hourly_rate,
    c.status,
    c.available_for_booking,
    c.rating,
    c.total_sessions,
    c.verified_at,
    c.created_at,
    -- 计算专业领域中文名称
    CASE c.specialization
        WHEN 'ANXIETY_DISORDERS' THEN '焦虑障碍'
        WHEN 'DEPRESSION' THEN '抑郁症'
        WHEN 'RELATIONSHIP_COUNSELING' THEN '情感咨询'
        WHEN 'FAMILY_THERAPY' THEN '家庭治疗'
        WHEN 'CHILD_PSYCHOLOGY' THEN '儿童心理'
        WHEN 'ADDICTION_COUNSELING' THEN '成瘾咨询'
        WHEN 'TRAUMA_THERAPY' THEN '创伤治疗'
        WHEN 'CAREER_COUNSELING' THEN '职业咨询'
        WHEN 'STRESS_MANAGEMENT' THEN '压力管理'
        WHEN 'GENERAL_COUNSELING' THEN '综合咨询'
        ELSE c.specialization
    END AS specialization_name,
    -- 计算状态中文名称
    CASE c.status
        WHEN 'PENDING' THEN '待审核'
        WHEN 'APPROVED' THEN '已认证'
        WHEN 'REJECTED' THEN '已拒绝'
        WHEN 'SUSPENDED' THEN '已暂停'
        ELSE c.status
    END AS status_name
FROM counselors c
JOIN users u ON c.user_id = u.id;

-- 可预约咨询师视图
CREATE OR REPLACE VIEW v_available_counselors AS
SELECT *
FROM v_counselor_detail
WHERE status = 'APPROVED' AND available_for_booking = TRUE;

-- ================================
-- 预约相关视图
-- ================================

-- 预约详细信息视图
CREATE OR REPLACE VIEW v_appointment_detail AS
SELECT 
    a.id,
    a.user_id,
    a.counselor_id,
    a.scheduled_time,
    a.actual_start_time,
    a.actual_end_time,
    a.fee,
    a.notes,
    a.status,
    a.confirmed_at,
    a.cancelled_at,
    a.cancellation_reason,
    a.cancelled_by,
    a.created_at,
    -- 用户信息
    u.username AS user_username,
    u.real_name AS user_real_name,
    u.phone AS user_phone,
    -- 咨询师信息
    cu.username AS counselor_username,
    cu.real_name AS counselor_real_name,
    c.specialization,
    c.license_number,
    -- 状态中文名称
    CASE a.status
        WHEN 'PENDING' THEN '待确认'
        WHEN 'CONFIRMED' THEN '已确认'
        WHEN 'IN_PROGRESS' THEN '进行中'
        WHEN 'COMPLETED' THEN '已完成'
        WHEN 'CANCELLED' THEN '已取消'
        WHEN 'NO_SHOW' THEN '未出席'
        ELSE a.status
    END AS status_name,
    -- 计算预约时长（分钟）
    CASE 
        WHEN a.actual_start_time IS NOT NULL AND a.actual_end_time IS NOT NULL 
        THEN TIMESTAMPDIFF(MINUTE, a.actual_start_time, a.actual_end_time)
        ELSE 60  -- 默认60分钟
    END AS duration_minutes,
    -- 判断是否为今日预约
    DATE(a.scheduled_time) = CURDATE() AS is_today,
    -- 判断是否即将开始（1小时内）
    a.scheduled_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 1 HOUR) AS is_upcoming
FROM appointments a
JOIN users u ON a.user_id = u.id
JOIN counselors c ON a.counselor_id = c.id
JOIN users cu ON c.user_id = cu.id;

-- ================================
-- 咨询会话相关视图
-- ================================

-- 咨询会话详细信息视图
CREATE OR REPLACE VIEW v_session_detail AS
SELECT 
    s.id,
    s.appointment_id,
    s.start_time,
    s.end_time,
    s.session_notes,
    s.counselor_notes,
    s.user_feedback,
    s.user_rating,
    s.status,
    s.is_emergency,
    s.emergency_notes,
    s.follow_up_required,
    s.follow_up_date,
    s.created_at,
    -- 预约信息
    a.user_id,
    a.counselor_id,
    a.scheduled_time,
    a.fee,
    -- 用户信息
    u.username AS user_username,
    u.real_name AS user_real_name,
    -- 咨询师信息
    cu.username AS counselor_username,
    cu.real_name AS counselor_real_name,
    c.specialization,
    -- 状态中文名称
    CASE s.status
        WHEN 'SCHEDULED' THEN '已安排'
        WHEN 'IN_PROGRESS' THEN '进行中'
        WHEN 'COMPLETED' THEN '已完成'
        WHEN 'CANCELLED' THEN '已取消'
        ELSE s.status
    END AS status_name,
    -- 计算会话时长（分钟）
    CASE 
        WHEN s.start_time IS NOT NULL AND s.end_time IS NOT NULL 
        THEN TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time)
        ELSE NULL
    END AS duration_minutes
FROM counseling_sessions s
JOIN appointments a ON s.appointment_id = a.id
JOIN users u ON a.user_id = u.id
JOIN counselors c ON a.counselor_id = c.id
JOIN users cu ON c.user_id = cu.id;

-- ================================
-- 统计相关视图
-- ================================

-- 用户统计视图
CREATE OR REPLACE VIEW v_user_stats AS
SELECT 
    u.id AS user_id,
    u.username,
    u.real_name,
    u.created_at AS register_date,
    -- 预约统计
    COUNT(a.id) AS total_appointments,
    COUNT(CASE WHEN a.status = 'COMPLETED' THEN 1 END) AS completed_appointments,
    COUNT(CASE WHEN a.status = 'CANCELLED' THEN 1 END) AS cancelled_appointments,
    COUNT(CASE WHEN a.status IN ('PENDING', 'CONFIRMED') THEN 1 END) AS upcoming_appointments,
    -- 会话统计
    COUNT(s.id) AS total_sessions,
    COUNT(CASE WHEN s.status = 'COMPLETED' THEN 1 END) AS completed_sessions,
    AVG(CASE WHEN s.user_rating IS NOT NULL THEN s.user_rating END) AS avg_rating_given,
    -- 费用统计
    SUM(CASE WHEN a.status = 'COMPLETED' THEN a.fee ELSE 0 END) AS total_spent,
    -- 最近活动
    MAX(a.scheduled_time) AS last_appointment_time,
    MAX(s.end_time) AS last_session_time
FROM users u
LEFT JOIN appointments a ON u.id = a.user_id
LEFT JOIN counseling_sessions s ON a.id = s.appointment_id
WHERE u.id NOT IN (SELECT DISTINCT user_id FROM counselors WHERE user_id IS NOT NULL)
GROUP BY u.id, u.username, u.real_name, u.created_at;

-- 咨询师统计视图
CREATE OR REPLACE VIEW v_counselor_stats AS
SELECT 
    c.id AS counselor_id,
    c.user_id,
    cu.username,
    cu.real_name,
    c.specialization,
    c.verified_at,
    c.hourly_rate,
    c.rating AS current_rating,
    c.total_sessions AS recorded_total_sessions,
    -- 预约统计
    COUNT(a.id) AS total_appointments,
    COUNT(CASE WHEN a.status = 'COMPLETED' THEN 1 END) AS completed_appointments,
    COUNT(CASE WHEN a.status = 'CANCELLED' THEN 1 END) AS cancelled_appointments,
    COUNT(CASE WHEN a.status IN ('PENDING', 'CONFIRMED') THEN 1 END) AS upcoming_appointments,
    -- 会话统计
    COUNT(s.id) AS total_sessions,
    COUNT(CASE WHEN s.status = 'COMPLETED' THEN 1 END) AS completed_sessions,
    AVG(CASE WHEN s.user_rating IS NOT NULL THEN s.user_rating END) AS avg_rating_received,
    COUNT(CASE WHEN s.user_rating IS NOT NULL THEN 1 END) AS total_ratings,
    -- 收入统计
    SUM(CASE WHEN a.status = 'COMPLETED' THEN a.fee ELSE 0 END) AS total_income,
    AVG(CASE WHEN a.status = 'COMPLETED' THEN a.fee END) AS avg_session_fee,
    -- 时间统计
    AVG(CASE 
        WHEN s.start_time IS NOT NULL AND s.end_time IS NOT NULL 
        THEN TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time)
        ELSE NULL 
    END) AS avg_session_duration,
    -- 最近活动
    MAX(a.scheduled_time) AS last_appointment_time,
    MAX(s.end_time) AS last_session_time,
    -- 紧急情况统计
    COUNT(CASE WHEN s.is_emergency = TRUE THEN 1 END) AS emergency_sessions,
    -- 跟进统计
    COUNT(CASE WHEN s.follow_up_required = TRUE THEN 1 END) AS follow_up_sessions
FROM counselors c
JOIN users cu ON c.user_id = cu.id
LEFT JOIN appointments a ON c.id = a.counselor_id
LEFT JOIN counseling_sessions s ON a.id = s.appointment_id
GROUP BY c.id, c.user_id, cu.username, cu.real_name, c.specialization, 
         c.verified_at, c.hourly_rate, c.rating, c.total_sessions;

-- 平台整体统计视图
CREATE OR REPLACE VIEW v_platform_stats AS
SELECT 
    -- 用户统计
    (SELECT COUNT(*) FROM users WHERE id NOT IN (SELECT DISTINCT user_id FROM counselors WHERE user_id IS NOT NULL)) AS total_users,
    (SELECT COUNT(*) FROM users WHERE status = 'ACTIVE' AND id NOT IN (SELECT DISTINCT user_id FROM counselors WHERE user_id IS NOT NULL)) AS active_users,
    (SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE() AND id NOT IN (SELECT DISTINCT user_id FROM counselors WHERE user_id IS NOT NULL)) AS new_users_today,
    
    -- 咨询师统计
    (SELECT COUNT(*) FROM counselors) AS total_counselors,
    (SELECT COUNT(*) FROM counselors WHERE status = 'APPROVED') AS approved_counselors,
    (SELECT COUNT(*) FROM counselors WHERE status = 'APPROVED' AND available_for_booking = TRUE) AS available_counselors,
    (SELECT COUNT(*) FROM counselors WHERE DATE(created_at) = CURDATE()) AS new_counselors_today,
    
    -- 预约统计
    (SELECT COUNT(*) FROM appointments) AS total_appointments,
    (SELECT COUNT(*) FROM appointments WHERE status = 'COMPLETED') AS completed_appointments,
    (SELECT COUNT(*) FROM appointments WHERE DATE(scheduled_time) = CURDATE()) AS appointments_today,
    (SELECT COUNT(*) FROM appointments WHERE status IN ('PENDING', 'CONFIRMED') AND scheduled_time > NOW()) AS upcoming_appointments,
    
    -- 会话统计
    (SELECT COUNT(*) FROM counseling_sessions) AS total_sessions,
    (SELECT COUNT(*) FROM counseling_sessions WHERE status = 'COMPLETED') AS completed_sessions,
    (SELECT AVG(user_rating) FROM counseling_sessions WHERE user_rating IS NOT NULL) AS avg_platform_rating,
    (SELECT COUNT(*) FROM counseling_sessions WHERE DATE(start_time) = CURDATE()) AS sessions_today,
    
    -- 收入统计
    (SELECT SUM(fee) FROM appointments WHERE status = 'COMPLETED') AS total_revenue,
    (SELECT SUM(fee) FROM appointments WHERE status = 'COMPLETED' AND DATE(actual_end_time) = CURDATE()) AS revenue_today,
    (SELECT AVG(fee) FROM appointments WHERE status = 'COMPLETED') AS avg_session_fee;

-- ================================
-- 业务查询视图
-- ================================

-- 需要跟进的会话视图
CREATE OR REPLACE VIEW v_sessions_need_follow_up AS
SELECT 
    s.*,
    u.real_name AS user_name,
    cu.real_name AS counselor_name,
    DATEDIFF(s.follow_up_date, CURDATE()) AS days_until_follow_up
FROM v_session_detail s
JOIN users u ON s.user_id = u.id
JOIN users cu ON s.counselor_id = cu.id
WHERE s.follow_up_required = TRUE 
  AND s.follow_up_date IS NOT NULL
  AND s.follow_up_date >= CURDATE()
ORDER BY s.follow_up_date ASC;

-- 今日预约视图
CREATE OR REPLACE VIEW v_today_appointments AS
SELECT *
FROM v_appointment_detail
WHERE DATE(scheduled_time) = CURDATE()
ORDER BY scheduled_time ASC;

-- 即将开始的预约视图（1小时内）
CREATE OR REPLACE VIEW v_upcoming_appointments AS
SELECT *
FROM v_appointment_detail
WHERE scheduled_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 1 HOUR)
  AND status IN ('CONFIRMED', 'PENDING')
ORDER BY scheduled_time ASC;
