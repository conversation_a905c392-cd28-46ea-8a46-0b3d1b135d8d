package com.psychology.platform.controller;

import com.psychology.platform.entity.Appointment;
import com.psychology.platform.service.AppointmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 预约控制器
 * 处理预约相关的HTTP请求
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/appointments")
@Tag(name = "预约管理", description = "预约创建、确认、取消等接口")
public class AppointmentController {

    @Autowired
    private AppointmentService appointmentService;

    @PostMapping
    @Operation(summary = "创建预约", description = "用户创建咨询预约")
    public ResponseEntity<Appointment> createAppointment(@Valid @RequestBody CreateAppointmentRequest request) {
        Appointment appointment = appointmentService.createAppointment(
            request.getUserId(),
            request.getCounselorId(),
            request.getScheduledTime(),
            request.getNotes()
        );
        return ResponseEntity.ok(appointment);
    }

    @PostMapping("/{id}/confirm")
    @Operation(summary = "确认预约", description = "咨询师确认预约")
    public ResponseEntity<Void> confirmAppointment(@Parameter(description = "预约ID") @PathVariable Long id) {
        appointmentService.confirmAppointment(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/cancel")
    @Operation(summary = "取消预约", description = "取消预约")
    public ResponseEntity<Void> cancelAppointment(@Parameter(description = "预约ID") @PathVariable Long id,
                                                @Valid @RequestBody CancelAppointmentRequest request) {
        appointmentService.cancelAppointment(id, request.getReason(), request.getCancelledBy());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/start")
    @Operation(summary = "开始咨询", description = "开始咨询会话")
    public ResponseEntity<Void> startSession(@Parameter(description = "预约ID") @PathVariable Long id) {
        appointmentService.startSession(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/complete")
    @Operation(summary = "完成咨询", description = "完成咨询会话")
    public ResponseEntity<Void> completeSession(@Parameter(description = "预约ID") @PathVariable Long id) {
        appointmentService.completeSession(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/no-show")
    @Operation(summary = "标记未出席", description = "标记预约为未出席")
    public ResponseEntity<Void> markAsNoShow(@Parameter(description = "预约ID") @PathVariable Long id) {
        appointmentService.markAsNoShow(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取预约详情", description = "根据预约ID获取详细信息")
    public ResponseEntity<Appointment> getAppointmentById(@Parameter(description = "预约ID") @PathVariable Long id) {
        Optional<Appointment> appointment = appointmentService.findById(id);
        return appointment.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "获取用户预约列表", description = "分页获取用户的预约列表")
    public ResponseEntity<Page<Appointment>> getUserAppointments(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Appointment> appointments = appointmentService.findAppointmentsByUserId(userId, pageable);
        return ResponseEntity.ok(appointments);
    }

    @GetMapping("/counselor/{counselorId}")
    @Operation(summary = "获取咨询师预约列表", description = "分页获取咨询师的预约列表")
    public ResponseEntity<Page<Appointment>> getCounselorAppointments(
            @Parameter(description = "咨询师ID") @PathVariable Long counselorId,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Appointment> appointments = appointmentService.findAppointmentsByCounselorId(counselorId, pageable);
        return ResponseEntity.ok(appointments);
    }

    @GetMapping("/status/{status}")
    @Operation(summary = "根据状态查询预约", description = "分页获取指定状态的预约列表")
    public ResponseEntity<Page<Appointment>> getAppointmentsByStatus(
            @Parameter(description = "预约状态") @PathVariable Appointment.AppointmentStatus status,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Appointment> appointments = appointmentService.findAppointmentsByStatus(status, pageable);
        return ResponseEntity.ok(appointments);
    }

    @GetMapping("/user/{userId}/today")
    @Operation(summary = "获取用户今日预约", description = "获取用户今天的预约列表")
    public ResponseEntity<List<Appointment>> getUserTodayAppointments(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        List<Appointment> appointments = appointmentService.findUserTodayAppointments(userId);
        return ResponseEntity.ok(appointments);
    }

    @GetMapping("/counselor/{counselorId}/today")
    @Operation(summary = "获取咨询师今日预约", description = "获取咨询师今天的预约列表")
    public ResponseEntity<List<Appointment>> getCounselorTodayAppointments(
            @Parameter(description = "咨询师ID") @PathVariable Long counselorId) {
        List<Appointment> appointments = appointmentService.findCounselorTodayAppointments(counselorId);
        return ResponseEntity.ok(appointments);
    }

    @GetMapping("/upcoming")
    @Operation(summary = "获取即将开始的预约", description = "获取即将开始的预约列表")
    public ResponseEntity<List<Appointment>> getUpcomingAppointments(
            @Parameter(description = "未来小时数") @RequestParam(defaultValue = "24") int hours) {
        List<Appointment> appointments = appointmentService.findUpcomingAppointments(hours);
        return ResponseEntity.ok(appointments);
    }

    @GetMapping("/check-conflict")
    @Operation(summary = "检查时间冲突", description = "检查咨询师在指定时间是否有冲突")
    public ResponseEntity<Boolean> checkTimeConflict(
            @Parameter(description = "咨询师ID") @RequestParam Long counselorId,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        boolean hasConflict = appointmentService.hasTimeConflict(counselorId, startTime, endTime);
        return ResponseEntity.ok(hasConflict);
    }

    @PostMapping("/send-reminders")
    @Operation(summary = "发送预约提醒", description = "发送预约提醒通知")
    public ResponseEntity<Void> sendReminders() {
        appointmentService.sendAppointmentReminders();
        return ResponseEntity.ok().build();
    }

    @GetMapping("/stats/user/{userId}/count")
    @Operation(summary = "统计用户预约数", description = "获取用户的预约总数")
    public ResponseEntity<Long> getUserAppointmentCount(@Parameter(description = "用户ID") @PathVariable Long userId) {
        long count = appointmentService.countUserAppointments(userId);
        return ResponseEntity.ok(count);
    }

    @GetMapping("/stats/counselor/{counselorId}/count")
    @Operation(summary = "统计咨询师预约数", description = "获取咨询师的预约总数")
    public ResponseEntity<Long> getCounselorAppointmentCount(@Parameter(description = "咨询师ID") @PathVariable Long counselorId) {
        long count = appointmentService.countCounselorAppointments(counselorId);
        return ResponseEntity.ok(count);
    }

    // 请求对象定义
    public static class CreateAppointmentRequest {
        private Long userId;
        private Long counselorId;
        private LocalDateTime scheduledTime;
        private String notes;

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        public Long getCounselorId() { return counselorId; }
        public void setCounselorId(Long counselorId) { this.counselorId = counselorId; }
        public LocalDateTime getScheduledTime() { return scheduledTime; }
        public void setScheduledTime(LocalDateTime scheduledTime) { this.scheduledTime = scheduledTime; }
        public String getNotes() { return notes; }
        public void setNotes(String notes) { this.notes = notes; }
    }

    public static class CancelAppointmentRequest {
        private String reason;
        private Appointment.CancelledBy cancelledBy;

        // Getters and Setters
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
        public Appointment.CancelledBy getCancelledBy() { return cancelledBy; }
        public void setCancelledBy(Appointment.CancelledBy cancelledBy) { this.cancelledBy = cancelledBy; }
    }
}
