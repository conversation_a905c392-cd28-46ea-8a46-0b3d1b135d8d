package com.psychology.platform.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 咨询师实体类
 * 包含咨询师专业信息、认证状态、服务配置等
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 */
@Entity
@Table(name = "counselors", indexes = {
    @Index(name = "idx_counselor_user_id", columnList = "user_id"),
    @Index(name = "idx_counselor_status", columnList = "status"),
    @Index(name = "idx_counselor_specialization", columnList = "specialization")
})
public class Counselor extends BaseEntity {

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false, unique = true)
    private User user;

    @NotBlank(message = "执业证书编号不能为空")
    @Size(max = 100, message = "执业证书编号长度不能超过100个字符")
    @Column(name = "license_number", nullable = false, unique = true, length = 100)
    private String licenseNumber;

    @Enumerated(EnumType.STRING)
    @Column(name = "specialization", nullable = false, length = 50)
    private Specialization specialization;

    @DecimalMin(value = "0.0", message = "咨询费用不能为负数")
    @DecimalMax(value = "9999.99", message = "咨询费用不能超过9999.99")
    @Column(name = "hourly_rate", precision = 6, scale = 2)
    private BigDecimal hourlyRate;

    @Size(max = 1000, message = "专业描述长度不能超过1000个字符")
    @Column(name = "professional_summary", length = 1000)
    private String professionalSummary;

    @Size(max = 200, message = "教育背景长度不能超过200个字符")
    @Column(name = "education", length = 200)
    private String education;

    @Column(name = "years_of_experience")
    private Integer yearsOfExperience;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private CounselorStatus status = CounselorStatus.PENDING;

    @Column(name = "verified_at")
    private LocalDateTime verifiedAt;

    @Size(max = 200, message = "认证文件URL长度不能超过200个字符")
    @Column(name = "certification_document_url", length = 200)
    private String certificationDocumentUrl;

    @Column(name = "available_for_booking", nullable = false)
    private Boolean availableForBooking = false;

    @DecimalMin(value = "0.0", message = "评分不能为负数")
    @DecimalMax(value = "5.0", message = "评分不能超过5.0")
    @Column(name = "rating", precision = 3, scale = 2)
    private BigDecimal rating = BigDecimal.ZERO;

    @Column(name = "total_sessions", nullable = false)
    private Integer totalSessions = 0;

    @Size(max = 500, message = "拒绝原因长度不能超过500个字符")
    @Column(name = "rejection_reason", length = 500)
    private String rejectionReason;

    // 枚举定义
    public enum Specialization {
        ANXIETY_DISORDERS("焦虑障碍"),
        DEPRESSION("抑郁症"),
        RELATIONSHIP_COUNSELING("情感咨询"),
        FAMILY_THERAPY("家庭治疗"),
        CHILD_PSYCHOLOGY("儿童心理"),
        ADDICTION_COUNSELING("成瘾咨询"),
        TRAUMA_THERAPY("创伤治疗"),
        CAREER_COUNSELING("职业咨询"),
        STRESS_MANAGEMENT("压力管理"),
        GENERAL_COUNSELING("综合咨询");

        private final String description;

        Specialization(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    public enum CounselorStatus {
        PENDING("待审核"),
        APPROVED("已认证"),
        REJECTED("已拒绝"),
        SUSPENDED("暂停服务"),
        INACTIVE("未激活");

        private final String description;

        CounselorStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // Constructors
    public Counselor() {}

    public Counselor(User user, String licenseNumber, Specialization specialization) {
        this.user = user;
        this.licenseNumber = licenseNumber;
        this.specialization = specialization;
    }

    // Getters and Setters
    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getLicenseNumber() {
        return licenseNumber;
    }

    public void setLicenseNumber(String licenseNumber) {
        this.licenseNumber = licenseNumber;
    }

    public Specialization getSpecialization() {
        return specialization;
    }

    public void setSpecialization(Specialization specialization) {
        this.specialization = specialization;
    }

    public BigDecimal getHourlyRate() {
        return hourlyRate;
    }

    public void setHourlyRate(BigDecimal hourlyRate) {
        this.hourlyRate = hourlyRate;
    }

    public String getProfessionalSummary() {
        return professionalSummary;
    }

    public void setProfessionalSummary(String professionalSummary) {
        this.professionalSummary = professionalSummary;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public Integer getYearsOfExperience() {
        return yearsOfExperience;
    }

    public void setYearsOfExperience(Integer yearsOfExperience) {
        this.yearsOfExperience = yearsOfExperience;
    }

    public CounselorStatus getStatus() {
        return status;
    }

    public void setStatus(CounselorStatus status) {
        this.status = status;
    }

    public LocalDateTime getVerifiedAt() {
        return verifiedAt;
    }

    public void setVerifiedAt(LocalDateTime verifiedAt) {
        this.verifiedAt = verifiedAt;
    }

    public String getCertificationDocumentUrl() {
        return certificationDocumentUrl;
    }

    public void setCertificationDocumentUrl(String certificationDocumentUrl) {
        this.certificationDocumentUrl = certificationDocumentUrl;
    }

    public Boolean getAvailableForBooking() {
        return availableForBooking;
    }

    public void setAvailableForBooking(Boolean availableForBooking) {
        this.availableForBooking = availableForBooking;
    }

    public BigDecimal getRating() {
        return rating;
    }

    public void setRating(BigDecimal rating) {
        this.rating = rating;
    }

    public Integer getTotalSessions() {
        return totalSessions;
    }

    public void setTotalSessions(Integer totalSessions) {
        this.totalSessions = totalSessions;
    }

    public String getRejectionReason() {
        return rejectionReason;
    }

    public void setRejectionReason(String rejectionReason) {
        this.rejectionReason = rejectionReason;
    }
}
