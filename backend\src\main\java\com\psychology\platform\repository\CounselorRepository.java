package com.psychology.platform.repository;

import com.psychology.platform.entity.Counselor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 咨询师数据访问层
 * 提供咨询师相关的数据库操作方法
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 */
@Repository
public interface CounselorRepository extends JpaRepository<Counselor, Long> {

    /**
     * 根据用户ID查找咨询师
     */
    Optional<Counselor> findByUserId(Long userId);

    /**
     * 根据执业证书编号查找咨询师
     */
    Optional<Counselor> findByLicenseNumber(String licenseNumber);

    /**
     * 检查执业证书编号是否存在
     */
    boolean existsByLicenseNumber(String licenseNumber);

    /**
     * 根据状态查找咨询师
     */
    Page<Counselor> findByStatus(Counselor.CounselorStatus status, Pageable pageable);

    /**
     * 根据专业领域查找咨询师
     */
    Page<Counselor> findBySpecialization(Counselor.Specialization specialization, Pageable pageable);

    /**
     * 查找可预约的咨询师
     */
    @Query("SELECT c FROM Counselor c WHERE c.availableForBooking = true AND c.status = 'APPROVED'")
    Page<Counselor> findAvailableCounselors(Pageable pageable);

    /**
     * 根据专业领域查找可预约的咨询师
     */
    @Query("SELECT c FROM Counselor c WHERE c.availableForBooking = true AND c.status = 'APPROVED' AND c.specialization = :specialization")
    Page<Counselor> findAvailableCounselorsBySpecialization(@Param("specialization") Counselor.Specialization specialization, Pageable pageable);

    /**
     * 根据费用范围查找咨询师
     */
    @Query("SELECT c FROM Counselor c WHERE c.hourlyRate BETWEEN :minRate AND :maxRate AND c.status = 'APPROVED'")
    Page<Counselor> findByHourlyRateBetween(@Param("minRate") BigDecimal minRate, 
                                           @Param("maxRate") BigDecimal maxRate, 
                                           Pageable pageable);

    /**
     * 根据评分查找咨询师
     */
    @Query("SELECT c FROM Counselor c WHERE c.rating >= :minRating AND c.status = 'APPROVED'")
    Page<Counselor> findByRatingGreaterThanEqual(@Param("minRating") BigDecimal minRating, Pageable pageable);

    /**
     * 根据经验年限查找咨询师
     */
    @Query("SELECT c FROM Counselor c WHERE c.yearsOfExperience >= :minYears AND c.status = 'APPROVED'")
    Page<Counselor> findByYearsOfExperienceGreaterThanEqual(@Param("minYears") Integer minYears, Pageable pageable);

    /**
     * 综合搜索咨询师
     */
    @Query("SELECT c FROM Counselor c JOIN c.user u WHERE " +
           "(c.specialization = :specialization OR :specialization IS NULL) AND " +
           "(c.hourlyRate BETWEEN :minRate AND :maxRate) AND " +
           "(c.rating >= :minRating OR :minRating IS NULL) AND " +
           "(c.yearsOfExperience >= :minYears OR :minYears IS NULL) AND " +
           "c.status = 'APPROVED' AND c.availableForBooking = true")
    Page<Counselor> searchCounselors(@Param("specialization") Counselor.Specialization specialization,
                                   @Param("minRate") BigDecimal minRate,
                                   @Param("maxRate") BigDecimal maxRate,
                                   @Param("minRating") BigDecimal minRating,
                                   @Param("minYears") Integer minYears,
                                   Pageable pageable);

    /**
     * 统计各状态的咨询师数量
     */
    long countByStatus(Counselor.CounselorStatus status);

    /**
     * 统计各专业领域的咨询师数量
     */
    long countBySpecialization(Counselor.Specialization specialization);

    /**
     * 查找评分最高的咨询师
     */
    @Query("SELECT c FROM Counselor c WHERE c.status = 'APPROVED' ORDER BY c.rating DESC, c.totalSessions DESC")
    List<Counselor> findTopRatedCounselors(Pageable pageable);

    /**
     * 查找最受欢迎的咨询师（按会话数量）
     */
    @Query("SELECT c FROM Counselor c WHERE c.status = 'APPROVED' ORDER BY c.totalSessions DESC, c.rating DESC")
    List<Counselor> findMostPopularCounselors(Pageable pageable);
}
