import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: { title: '首页' }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: { title: '登录', guest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: { title: '注册', guest: true }
  },
  {
    path: '/counselors',
    name: 'Counselors',
    component: () => import('@/views/counselors/CounselorList.vue'),
    meta: { title: '咨询师列表' }
  },
  {
    path: '/counselors/:id',
    name: 'CounselorDetail',
    component: () => import('@/views/counselors/CounselorDetail.vue'),
    meta: { title: '咨询师详情' }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/dashboard/Dashboard.vue'),
    meta: { title: '个人中心', requiresAuth: true },
    children: [
      {
        path: '',
        name: 'DashboardHome',
        component: () => import('@/views/dashboard/DashboardHome.vue'),
        meta: { title: '概览' }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/dashboard/Profile.vue'),
        meta: { title: '个人信息' }
      },
      {
        path: 'appointments',
        name: 'Appointments',
        component: () => import('@/views/dashboard/Appointments.vue'),
        meta: { title: '我的预约' }
      },
      {
        path: 'sessions',
        name: 'Sessions',
        component: () => import('@/views/dashboard/Sessions.vue'),
        meta: { title: '咨询记录' }
      },
      {
        path: 'counselor-apply',
        name: 'CounselorApply',
        component: () => import('@/views/dashboard/CounselorApply.vue'),
        meta: { title: '申请成为咨询师' }
      },
      {
        path: 'counselor-center',
        name: 'CounselorCenter',
        component: () => import('@/views/dashboard/CounselorCenter.vue'),
        meta: { title: '咨询师中心', requiresCounselor: true }
      }
    ]
  },
  {
    path: '/booking/:counselorId',
    name: 'Booking',
    component: () => import('@/views/booking/Booking.vue'),
    meta: { title: '预约咨询', requiresAuth: true }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/NotFound.vue'),
    meta: { title: '页面未找到' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  const userStore = useUserStore()
  
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 心理咨询服务平台` : '心理咨询服务平台'
  
  // 检查认证状态
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next('/login')
    return
  }
  
  // 检查访客页面
  if (to.meta.guest && userStore.isLoggedIn) {
    next('/dashboard')
    return
  }
  
  // 检查咨询师权限
  if (to.meta.requiresCounselor && !userStore.isCounselor) {
    next('/dashboard')
    return
  }
  
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
