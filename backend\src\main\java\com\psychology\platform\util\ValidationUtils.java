package com.psychology.platform.util;

import java.util.regex.Pattern;

/**
 * 验证工具类
 * 提供常用的数据验证方法
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 */
public class ValidationUtils {

    // 邮箱正则表达式
    private static final String EMAIL_PATTERN = 
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
    
    // 手机号正则表达式（中国大陆）
    private static final String PHONE_PATTERN = "^1[3-9]\\d{9}$";
    
    // 密码强度正则表达式（至少8位，包含字母和数字）
    private static final String PASSWORD_PATTERN = "^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d@$!%*#?&]{8,}$";
    
    // 用户名正则表达式（2-20位字母、数字、下划线）
    private static final String USERNAME_PATTERN = "^[a-zA-Z0-9_]{2,20}$";

    private static final Pattern emailPattern = Pattern.compile(EMAIL_PATTERN);
    private static final Pattern phonePattern = Pattern.compile(PHONE_PATTERN);
    private static final Pattern passwordPattern = Pattern.compile(PASSWORD_PATTERN);
    private static final Pattern usernamePattern = Pattern.compile(USERNAME_PATTERN);

    /**
     * 验证邮箱格式
     */
    public static boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        return emailPattern.matcher(email.trim()).matches();
    }

    /**
     * 验证手机号格式
     */
    public static boolean isValidPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return false;
        }
        return phonePattern.matcher(phone.trim()).matches();
    }

    /**
     * 验证密码强度
     */
    public static boolean isValidPassword(String password) {
        if (password == null || password.trim().isEmpty()) {
            return false;
        }
        return passwordPattern.matcher(password).matches();
    }

    /**
     * 验证用户名格式
     */
    public static boolean isValidUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            return false;
        }
        return usernamePattern.matcher(username.trim()).matches();
    }

    /**
     * 验证字符串是否为空或null
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 验证字符串是否不为空
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * 验证字符串长度是否在指定范围内
     */
    public static boolean isLengthValid(String str, int minLength, int maxLength) {
        if (str == null) {
            return false;
        }
        int length = str.trim().length();
        return length >= minLength && length <= maxLength;
    }

    /**
     * 验证数字是否在指定范围内
     */
    public static boolean isNumberInRange(Number number, Number min, Number max) {
        if (number == null || min == null || max == null) {
            return false;
        }
        double value = number.doubleValue();
        double minValue = min.doubleValue();
        double maxValue = max.doubleValue();
        return value >= minValue && value <= maxValue;
    }

    /**
     * 验证身份证号格式（简单验证）
     */
    public static boolean isValidIdCard(String idCard) {
        if (idCard == null || idCard.trim().isEmpty()) {
            return false;
        }
        String trimmed = idCard.trim();
        // 18位身份证号
        if (trimmed.length() == 18) {
            return trimmed.matches("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");
        }
        // 15位身份证号
        if (trimmed.length() == 15) {
            return trimmed.matches("^[1-9]\\d{5}\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}$");
        }
        return false;
    }

    /**
     * 验证URL格式
     */
    public static boolean isValidUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        String urlPattern = "^(https?|ftp)://[^\\s/$.?#].[^\\s]*$";
        return Pattern.matches(urlPattern, url.trim());
    }

    /**
     * 验证IP地址格式
     */
    public static boolean isValidIpAddress(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }
        String ipPattern = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
        return Pattern.matches(ipPattern, ip.trim());
    }

    /**
     * 验证执业证书编号格式（示例格式）
     */
    public static boolean isValidLicenseNumber(String licenseNumber) {
        if (licenseNumber == null || licenseNumber.trim().isEmpty()) {
            return false;
        }
        // 假设执业证书编号为6-20位字母数字组合
        String licensePattern = "^[A-Za-z0-9]{6,20}$";
        return Pattern.matches(licensePattern, licenseNumber.trim());
    }

    /**
     * 验证评分范围（1-5分）
     */
    public static boolean isValidRating(Integer rating) {
        return rating != null && rating >= 1 && rating <= 5;
    }

    /**
     * 验证年龄范围
     */
    public static boolean isValidAge(Integer age) {
        return age != null && age >= 0 && age <= 150;
    }

    /**
     * 验证经验年限
     */
    public static boolean isValidExperienceYears(Integer years) {
        return years != null && years >= 0 && years <= 50;
    }
}
