package com.psychology.platform.service.impl;

import com.psychology.platform.entity.Appointment;
import com.psychology.platform.entity.CounselingSession;
import com.psychology.platform.exception.BusinessException;
import com.psychology.platform.exception.ResourceNotFoundException;
import com.psychology.platform.repository.AppointmentRepository;
import com.psychology.platform.repository.CounselingSessionRepository;
import com.psychology.platform.service.CounselingSessionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 咨询会话服务实现类
 * 实现咨询会话相关的业务逻辑
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 */
@Service
@Transactional
public class CounselingSessionServiceImpl implements CounselingSessionService {

    @Autowired
    private CounselingSessionRepository counselingSessionRepository;

    @Autowired
    private AppointmentRepository appointmentRepository;

    @Override
    public CounselingSession createSession(Long appointmentId) {
        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new ResourceNotFoundException("预约", "id", appointmentId));

        // 检查是否已存在会话
        if (counselingSessionRepository.findByAppointmentId(appointmentId).isPresent()) {
            throw new BusinessException("该预约已存在咨询会话");
        }

        // 创建咨询会话
        CounselingSession session = new CounselingSession();
        session.setAppointment(appointment);
        session.setStatus(CounselingSession.SessionStatus.SCHEDULED);

        return counselingSessionRepository.save(session);
    }

    @Override
    public void startSession(Long sessionId) {
        CounselingSession session = counselingSessionRepository.findById(sessionId)
                .orElseThrow(() -> new ResourceNotFoundException("咨询会话", "id", sessionId));

        if (session.getStatus() != CounselingSession.SessionStatus.SCHEDULED) {
            throw new BusinessException("只能开始已安排的咨询会话");
        }

        session.setStatus(CounselingSession.SessionStatus.IN_PROGRESS);
        session.setStartTime(LocalDateTime.now());
        counselingSessionRepository.save(session);
    }

    @Override
    public void endSession(Long sessionId, String sessionNotes, String counselorNotes) {
        CounselingSession session = counselingSessionRepository.findById(sessionId)
                .orElseThrow(() -> new ResourceNotFoundException("咨询会话", "id", sessionId));

        if (session.getStatus() != CounselingSession.SessionStatus.IN_PROGRESS) {
            throw new BusinessException("只能结束进行中的咨询会话");
        }

        session.setStatus(CounselingSession.SessionStatus.COMPLETED);
        session.setEndTime(LocalDateTime.now());
        session.setSessionNotes(sessionNotes);
        session.setCounselorNotes(counselorNotes);
        counselingSessionRepository.save(session);
    }

    @Override
    public void cancelSession(Long sessionId) {
        CounselingSession session = counselingSessionRepository.findById(sessionId)
                .orElseThrow(() -> new ResourceNotFoundException("咨询会话", "id", sessionId));

        if (session.getStatus() == CounselingSession.SessionStatus.COMPLETED) {
            throw new BusinessException("无法取消已完成的咨询会话");
        }

        session.setStatus(CounselingSession.SessionStatus.CANCELLED);
        counselingSessionRepository.save(session);
    }

    @Override
    public void addUserFeedback(Long sessionId, String feedback, Integer rating) {
        CounselingSession session = counselingSessionRepository.findById(sessionId)
                .orElseThrow(() -> new ResourceNotFoundException("咨询会话", "id", sessionId));

        if (session.getStatus() != CounselingSession.SessionStatus.COMPLETED) {
            throw new BusinessException("只能对已完成的咨询会话进行评价");
        }

        if (rating != null && (rating < 1 || rating > 5)) {
            throw new BusinessException("评分必须在1-5之间");
        }

        session.setUserFeedback(feedback);
        session.setUserRating(rating);
        counselingSessionRepository.save(session);

        // 更新咨询师评分
        // TODO: 触发咨询师评分更新
    }

    @Override
    public void markAsEmergency(Long sessionId, String emergencyNotes) {
        CounselingSession session = counselingSessionRepository.findById(sessionId)
                .orElseThrow(() -> new ResourceNotFoundException("咨询会话", "id", sessionId));

        session.setIsEmergency(true);
        session.setEmergencyNotes(emergencyNotes);
        counselingSessionRepository.save(session);

        // TODO: 触发紧急情况处理流程
    }

    @Override
    public void setFollowUpRequired(Long sessionId, LocalDateTime followUpDate) {
        CounselingSession session = counselingSessionRepository.findById(sessionId)
                .orElseThrow(() -> new ResourceNotFoundException("咨询会话", "id", sessionId));

        session.setFollowUpRequired(true);
        session.setFollowUpDate(followUpDate);
        counselingSessionRepository.save(session);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<CounselingSession> findById(Long id) {
        return counselingSessionRepository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<CounselingSession> findByAppointmentId(Long appointmentId) {
        return counselingSessionRepository.findByAppointmentId(appointmentId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CounselingSession> findSessionsByUserId(Long userId, Pageable pageable) {
        return counselingSessionRepository.findByUserId(userId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CounselingSession> findSessionsByCounselorId(Long counselorId, Pageable pageable) {
        return counselingSessionRepository.findByCounselorId(counselorId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CounselingSession> findSessionsByStatus(CounselingSession.SessionStatus status, Pageable pageable) {
        return counselingSessionRepository.findByStatus(status, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<CounselingSession> findSessionsNeedingFollowUp() {
        return counselingSessionRepository.findSessionsNeedingFollowUp(LocalDateTime.now());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CounselingSession> findEmergencySessions(Pageable pageable) {
        return counselingSessionRepository.findByIsEmergency(true, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<CounselingSession> findTodayInProgressSessions() {
        return counselingSessionRepository.findTodayInProgressSessions(LocalDateTime.now());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CounselingSession> findCompletedSessionsWithoutRating(Pageable pageable) {
        return counselingSessionRepository.findCompletedSessionsWithoutRating(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public long countCompletedSessionsByUserId(Long userId) {
        return counselingSessionRepository.countCompletedSessionsByUserId(userId);
    }

    @Override
    @Transactional(readOnly = true)
    public long countCompletedSessionsByCounselorId(Long counselorId) {
        return counselingSessionRepository.countCompletedSessionsByCounselorId(counselorId);
    }

    @Override
    @Transactional(readOnly = true)
    public Double calculateAverageRatingByCounselorId(Long counselorId) {
        return counselingSessionRepository.calculateAverageRatingByCounselorId(counselorId);
    }

    @Override
    @Transactional(readOnly = true)
    public long countSessionsByStatus(CounselingSession.SessionStatus status) {
        return counselingSessionRepository.countByStatus(status);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CounselingSession> findSessionsByTimeRange(LocalDateTime startTime, 
                                                          LocalDateTime endTime, 
                                                          Pageable pageable) {
        return counselingSessionRepository.findByStartTimeBetween(startTime, endTime, pageable);
    }
}
