import request from './request'

// 创建预约
export const createAppointment = (data) => {
  return request({
    url: '/appointments',
    method: 'post',
    data
  })
}

// 获取预约详情
export const getAppointmentDetail = (id) => {
  return request({
    url: `/appointments/${id}`,
    method: 'get'
  })
}

// 获取用户预约列表
export const getUserAppointments = (userId, params) => {
  return request({
    url: `/appointments/user/${userId}`,
    method: 'get',
    params
  })
}

// 获取咨询师预约列表
export const getCounselorAppointments = (counselorId, params) => {
  return request({
    url: `/appointments/counselor/${counselorId}`,
    method: 'get',
    params
  })
}

// 根据状态获取预约列表
export const getAppointmentsByStatus = (status, params) => {
  return request({
    url: `/appointments/status/${status}`,
    method: 'get',
    params
  })
}

// 获取用户今日预约
export const getUserTodayAppointments = (userId) => {
  return request({
    url: `/appointments/user/${userId}/today`,
    method: 'get'
  })
}

// 获取咨询师今日预约
export const getCounselorTodayAppointments = (counselorId) => {
  return request({
    url: `/appointments/counselor/${counselorId}/today`,
    method: 'get'
  })
}

// 获取即将开始的预约
export const getUpcomingAppointments = (hours = 24) => {
  return request({
    url: '/appointments/upcoming',
    method: 'get',
    params: { hours }
  })
}

// 确认预约
export const confirmAppointment = (id) => {
  return request({
    url: `/appointments/${id}/confirm`,
    method: 'post'
  })
}

// 取消预约
export const cancelAppointment = (id, data) => {
  return request({
    url: `/appointments/${id}/cancel`,
    method: 'post',
    data
  })
}

// 开始咨询
export const startSession = (id) => {
  return request({
    url: `/appointments/${id}/start`,
    method: 'post'
  })
}

// 完成咨询
export const completeSession = (id) => {
  return request({
    url: `/appointments/${id}/complete`,
    method: 'post'
  })
}

// 标记为未出席
export const markAsNoShow = (id) => {
  return request({
    url: `/appointments/${id}/no-show`,
    method: 'post'
  })
}

// 检查时间冲突
export const checkTimeConflict = (counselorId, startTime, endTime) => {
  return request({
    url: '/appointments/check-conflict',
    method: 'get',
    params: {
      counselorId,
      startTime,
      endTime
    }
  })
}

// 发送预约提醒
export const sendAppointmentReminders = () => {
  return request({
    url: '/appointments/send-reminders',
    method: 'post'
  })
}

// 统计用户预约数
export const getUserAppointmentCount = (userId) => {
  return request({
    url: `/appointments/stats/user/${userId}/count`,
    method: 'get'
  })
}

// 统计咨询师预约数
export const getCounselorAppointmentCount = (counselorId) => {
  return request({
    url: `/appointments/stats/counselor/${counselorId}/count`,
    method: 'get'
  })
}
