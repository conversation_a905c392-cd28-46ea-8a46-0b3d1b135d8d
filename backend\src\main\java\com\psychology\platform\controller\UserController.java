package com.psychology.platform.controller;

import com.psychology.platform.entity.User;
import com.psychology.platform.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * 用户控制器
 * 处理用户相关的HTTP请求
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/users")
@Tag(name = "用户管理", description = "用户注册、登录、信息管理等接口")
public class UserController {

    @Autowired
    private UserService userService;

    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "创建新用户账户")
    public ResponseEntity<User> register(@Valid @RequestBody UserRegistrationRequest request) {
        User user = userService.register(request.getUsername(), request.getPassword(), request.getEmail());
        return ResponseEntity.ok(user);
    }

    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户身份验证")
    public ResponseEntity<User> login(@Valid @RequestBody UserLoginRequest request) {
        User user = userService.login(request.getEmailOrUsername(), request.getPassword());
        return ResponseEntity.ok(user);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户详细信息")
    public ResponseEntity<User> getUserById(@Parameter(description = "用户ID") @PathVariable Long id) {
        Optional<User> user = userService.findById(id);
        return user.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新用户信息", description = "更新用户的个人信息")
    public ResponseEntity<User> updateUser(@Parameter(description = "用户ID") @PathVariable Long id,
                                         @Valid @RequestBody UserUpdateRequest request) {
        User userDetails = new User();
        userDetails.setRealName(request.getRealName());
        userDetails.setPhone(request.getPhone());
        userDetails.setGender(request.getGender());
        userDetails.setBirthDate(request.getBirthDate());
        userDetails.setAvatarUrl(request.getAvatarUrl());
        userDetails.setBio(request.getBio());

        User updatedUser = userService.updateUser(id, userDetails);
        return ResponseEntity.ok(updatedUser);
    }

    @PostMapping("/{id}/change-password")
    @Operation(summary = "修改密码", description = "用户修改登录密码")
    public ResponseEntity<Void> changePassword(@Parameter(description = "用户ID") @PathVariable Long id,
                                             @Valid @RequestBody ChangePasswordRequest request) {
        userService.changePassword(id, request.getOldPassword(), request.getNewPassword());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/reset-password")
    @Operation(summary = "重置密码", description = "通过邮箱重置密码")
    public ResponseEntity<Void> resetPassword(@Valid @RequestBody ResetPasswordRequest request) {
        userService.resetPassword(request.getEmail());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/verify-email")
    @Operation(summary = "验证邮箱", description = "验证用户邮箱地址")
    public ResponseEntity<Void> verifyEmail(@Valid @RequestBody EmailVerificationRequest request) {
        userService.verifyEmail(request.getEmail(), request.getVerificationCode());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/verify-phone")
    @Operation(summary = "验证手机号", description = "验证用户手机号码")
    public ResponseEntity<Void> verifyPhone(@Valid @RequestBody PhoneVerificationRequest request) {
        userService.verifyPhone(request.getPhone(), request.getVerificationCode());
        return ResponseEntity.ok().build();
    }

    @GetMapping
    @Operation(summary = "分页查询用户", description = "分页获取用户列表")
    public ResponseEntity<Page<User>> getAllUsers(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<User> users = userService.findAllUsers(pageable);
        return ResponseEntity.ok(users);
    }

    @GetMapping("/search")
    @Operation(summary = "搜索用户", description = "根据关键字搜索用户")
    public ResponseEntity<Page<User>> searchUsers(
            @Parameter(description = "搜索关键字") @RequestParam String keyword,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<User> users = userService.searchUsers(keyword, pageable);
        return ResponseEntity.ok(users);
    }

    @PostMapping("/{id}/activate")
    @Operation(summary = "激活用户", description = "激活用户账户")
    public ResponseEntity<Void> activateUser(@Parameter(description = "用户ID") @PathVariable Long id) {
        userService.activateUser(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/deactivate")
    @Operation(summary = "停用用户", description = "停用用户账户")
    public ResponseEntity<Void> deactivateUser(@Parameter(description = "用户ID") @PathVariable Long id) {
        userService.deactivateUser(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/ban")
    @Operation(summary = "封禁用户", description = "封禁用户账户")
    public ResponseEntity<Void> banUser(@Parameter(description = "用户ID") @PathVariable Long id) {
        userService.banUser(id);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户", description = "软删除用户账户")
    public ResponseEntity<Void> deleteUser(@Parameter(description = "用户ID") @PathVariable Long id) {
        userService.deleteUser(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/stats/total")
    @Operation(summary = "用户总数统计", description = "获取用户总数")
    public ResponseEntity<Long> getTotalUserCount() {
        long count = userService.countTotalUsers();
        return ResponseEntity.ok(count);
    }

    @GetMapping("/check/email")
    @Operation(summary = "检查邮箱是否存在", description = "验证邮箱是否已被注册")
    public ResponseEntity<Boolean> checkEmailExists(@Parameter(description = "邮箱地址") @RequestParam String email) {
        boolean exists = userService.isEmailExists(email);
        return ResponseEntity.ok(exists);
    }

    @GetMapping("/check/username")
    @Operation(summary = "检查用户名是否存在", description = "验证用户名是否已被注册")
    public ResponseEntity<Boolean> checkUsernameExists(@Parameter(description = "用户名") @RequestParam String username) {
        boolean exists = userService.isUsernameExists(username);
        return ResponseEntity.ok(exists);
    }

    // 内部类定义请求对象
    public static class UserRegistrationRequest {
        private String username;
        private String password;
        private String email;

        // Getters and Setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
    }

    public static class UserLoginRequest {
        private String emailOrUsername;
        private String password;

        // Getters and Setters
        public String getEmailOrUsername() { return emailOrUsername; }
        public void setEmailOrUsername(String emailOrUsername) { this.emailOrUsername = emailOrUsername; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
    }

    public static class UserUpdateRequest {
        private String realName;
        private String phone;
        private User.Gender gender;
        private java.time.LocalDate birthDate;
        private String avatarUrl;
        private String bio;

        // Getters and Setters
        public String getRealName() { return realName; }
        public void setRealName(String realName) { this.realName = realName; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public User.Gender getGender() { return gender; }
        public void setGender(User.Gender gender) { this.gender = gender; }
        public java.time.LocalDate getBirthDate() { return birthDate; }
        public void setBirthDate(java.time.LocalDate birthDate) { this.birthDate = birthDate; }
        public String getAvatarUrl() { return avatarUrl; }
        public void setAvatarUrl(String avatarUrl) { this.avatarUrl = avatarUrl; }
        public String getBio() { return bio; }
        public void setBio(String bio) { this.bio = bio; }
    }

    public static class ChangePasswordRequest {
        private String oldPassword;
        private String newPassword;

        // Getters and Setters
        public String getOldPassword() { return oldPassword; }
        public void setOldPassword(String oldPassword) { this.oldPassword = oldPassword; }
        public String getNewPassword() { return newPassword; }
        public void setNewPassword(String newPassword) { this.newPassword = newPassword; }
    }

    public static class ResetPasswordRequest {
        private String email;

        // Getters and Setters
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
    }

    public static class EmailVerificationRequest {
        private String email;
        private String verificationCode;

        // Getters and Setters
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getVerificationCode() { return verificationCode; }
        public void setVerificationCode(String verificationCode) { this.verificationCode = verificationCode; }
    }

    public static class PhoneVerificationRequest {
        private String phone;
        private String verificationCode;

        // Getters and Setters
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public String getVerificationCode() { return verificationCode; }
        public void setVerificationCode(String verificationCode) { this.verificationCode = verificationCode; }
    }
}
