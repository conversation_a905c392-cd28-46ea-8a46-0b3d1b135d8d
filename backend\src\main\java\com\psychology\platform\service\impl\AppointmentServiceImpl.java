package com.psychology.platform.service.impl;

import com.psychology.platform.entity.Appointment;
import com.psychology.platform.entity.Counselor;
import com.psychology.platform.entity.User;
import com.psychology.platform.exception.BusinessException;
import com.psychology.platform.exception.ResourceNotFoundException;
import com.psychology.platform.repository.AppointmentRepository;
import com.psychology.platform.repository.CounselorRepository;
import com.psychology.platform.repository.UserRepository;
import com.psychology.platform.service.AppointmentService;
import com.psychology.platform.util.DateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 预约服务实现类
 * 实现预约相关的业务逻辑
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 */
@Service
@Transactional
public class AppointmentServiceImpl implements AppointmentService {

    @Autowired
    private AppointmentRepository appointmentRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CounselorRepository counselorRepository;

    @Override
    public Appointment createAppointment(Long userId, Long counselorId, 
                                       LocalDateTime scheduledTime, String notes) {
        // 验证用户和咨询师
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("用户", "id", userId));
        
        Counselor counselor = counselorRepository.findById(counselorId)
                .orElseThrow(() -> new ResourceNotFoundException("咨询师", "id", counselorId));

        // 检查咨询师是否可预约
        if (!counselor.getAvailableForBooking() || counselor.getStatus() != Counselor.CounselorStatus.APPROVED) {
            throw new BusinessException("该咨询师当前不可预约");
        }

        // 检查预约时间是否为未来时间
        if (!DateTimeUtils.isFuture(scheduledTime)) {
            throw new BusinessException("预约时间必须是未来时间");
        }

        // 检查时间冲突
        LocalDateTime endTime = scheduledTime.plusMinutes(60); // 默认60分钟
        if (hasTimeConflict(counselorId, scheduledTime, endTime)) {
            throw new BusinessException("该时间段已被预约");
        }

        // 创建预约
        Appointment appointment = new Appointment();
        appointment.setUser(user);
        appointment.setCounselor(counselor);
        appointment.setScheduledTime(scheduledTime);
        appointment.setNotes(notes);
        appointment.setFee(counselor.getHourlyRate());
        appointment.setStatus(Appointment.AppointmentStatus.PENDING);

        return appointmentRepository.save(appointment);
    }

    @Override
    public void confirmAppointment(Long appointmentId) {
        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new ResourceNotFoundException("预约", "id", appointmentId));

        if (appointment.getStatus() != Appointment.AppointmentStatus.PENDING) {
            throw new BusinessException("只能确认待确认状态的预约");
        }

        appointment.setStatus(Appointment.AppointmentStatus.CONFIRMED);
        appointment.setConfirmedAt(LocalDateTime.now());
        appointmentRepository.save(appointment);
    }

    @Override
    public void cancelAppointment(Long appointmentId, String reason, Appointment.CancelledBy cancelledBy) {
        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new ResourceNotFoundException("预约", "id", appointmentId));

        if (appointment.getStatus() == Appointment.AppointmentStatus.COMPLETED ||
            appointment.getStatus() == Appointment.AppointmentStatus.CANCELLED) {
            throw new BusinessException("无法取消已完成或已取消的预约");
        }

        // 检查取消时间限制（提前24小时）
        long hoursUntilAppointment = DateTimeUtils.getHoursBetween(LocalDateTime.now(), appointment.getScheduledTime());
        if (hoursUntilAppointment < 24) {
            throw new BusinessException("取消预约需要提前24小时");
        }

        appointment.setStatus(Appointment.AppointmentStatus.CANCELLED);
        appointment.setCancelledAt(LocalDateTime.now());
        appointment.setCancellationReason(reason);
        appointment.setCancelledBy(cancelledBy);
        appointmentRepository.save(appointment);
    }

    @Override
    public void startSession(Long appointmentId) {
        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new ResourceNotFoundException("预约", "id", appointmentId));

        if (appointment.getStatus() != Appointment.AppointmentStatus.CONFIRMED) {
            throw new BusinessException("只能开始已确认的预约");
        }

        appointment.setStatus(Appointment.AppointmentStatus.IN_PROGRESS);
        appointment.setActualStartTime(LocalDateTime.now());
        appointmentRepository.save(appointment);
    }

    @Override
    public void completeSession(Long appointmentId) {
        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new ResourceNotFoundException("预约", "id", appointmentId));

        if (appointment.getStatus() != Appointment.AppointmentStatus.IN_PROGRESS) {
            throw new BusinessException("只能完成进行中的预约");
        }

        appointment.setStatus(Appointment.AppointmentStatus.COMPLETED);
        appointment.setActualEndTime(LocalDateTime.now());
        appointmentRepository.save(appointment);
    }

    @Override
    public void markAsNoShow(Long appointmentId) {
        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new ResourceNotFoundException("预约", "id", appointmentId));

        if (appointment.getStatus() != Appointment.AppointmentStatus.CONFIRMED) {
            throw new BusinessException("只能标记已确认的预约为未出席");
        }

        appointment.setStatus(Appointment.AppointmentStatus.NO_SHOW);
        appointmentRepository.save(appointment);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Appointment> findById(Long id) {
        return appointmentRepository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Appointment> findAppointmentsByUserId(Long userId, Pageable pageable) {
        return appointmentRepository.findByUserId(userId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Appointment> findAppointmentsByCounselorId(Long counselorId, Pageable pageable) {
        return appointmentRepository.findByCounselorId(counselorId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Appointment> findAppointmentsByStatus(Appointment.AppointmentStatus status, Pageable pageable) {
        return appointmentRepository.findByStatus(status, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Appointment> findUserTodayAppointments(Long userId) {
        return appointmentRepository.findUserTodayAppointments(userId, LocalDateTime.now());
    }

    @Override
    @Transactional(readOnly = true)
    public List<Appointment> findCounselorTodayAppointments(Long counselorId) {
        return appointmentRepository.findCounselorTodayAppointments(counselorId, LocalDateTime.now());
    }

    @Override
    @Transactional(readOnly = true)
    public List<Appointment> findUpcomingAppointments(int hours) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime upcomingTime = now.plusHours(hours);
        return appointmentRepository.findUpcomingAppointments(now, upcomingTime);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasTimeConflict(Long counselorId, LocalDateTime startTime, LocalDateTime endTime) {
        return appointmentRepository.existsConflictingAppointment(counselorId, startTime, endTime);
    }

    @Override
    public void sendAppointmentReminders() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime reminderTime = now.plusHours(1); // 提前1小时提醒
        
        List<Appointment> appointmentsNeedingReminder = 
            appointmentRepository.findAppointmentsNeedingReminder(now, reminderTime);
        
        for (Appointment appointment : appointmentsNeedingReminder) {
            // TODO: 实现发送提醒的逻辑（邮件、短信等）
            appointment.setReminderSent(true);
            appointmentRepository.save(appointment);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public long countUserAppointments(Long userId) {
        return appointmentRepository.countByUserId(userId);
    }

    @Override
    @Transactional(readOnly = true)
    public long countCounselorAppointments(Long counselorId) {
        return appointmentRepository.countByCounselorId(counselorId);
    }

    @Override
    @Transactional(readOnly = true)
    public long countAppointmentsByStatus(Appointment.AppointmentStatus status) {
        return appointmentRepository.countByStatus(status);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Appointment> findAppointmentsByTimeRange(LocalDateTime startTime, 
                                                        LocalDateTime endTime, 
                                                        Pageable pageable) {
        return appointmentRepository.findByScheduledTimeBetween(startTime, endTime, pageable);
    }
}
