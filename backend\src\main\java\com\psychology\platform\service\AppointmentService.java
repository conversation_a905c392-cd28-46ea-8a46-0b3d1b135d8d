package com.psychology.platform.service;

import com.psychology.platform.entity.Appointment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 预约服务接口
 * 定义预约相关的业务逻辑方法
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 */
public interface AppointmentService {

    /**
     * 创建预约
     */
    Appointment createAppointment(Long userId, Long counselorId, 
                                LocalDateTime scheduledTime, String notes);

    /**
     * 确认预约
     */
    void confirmAppointment(Long appointmentId);

    /**
     * 取消预约
     */
    void cancelAppointment(Long appointmentId, String reason, 
                          Appointment.CancelledBy cancelledBy);

    /**
     * 开始咨询会话
     */
    void startSession(Long appointmentId);

    /**
     * 完成咨询会话
     */
    void completeSession(Long appointmentId);

    /**
     * 标记为未出席
     */
    void markAsNoShow(Long appointmentId);

    /**
     * 根据ID查找预约
     */
    Optional<Appointment> findById(Long id);

    /**
     * 根据用户ID分页查询预约
     */
    Page<Appointment> findAppointmentsByUserId(Long userId, Pageable pageable);

    /**
     * 根据咨询师ID分页查询预约
     */
    Page<Appointment> findAppointmentsByCounselorId(Long counselorId, Pageable pageable);

    /**
     * 根据状态分页查询预约
     */
    Page<Appointment> findAppointmentsByStatus(Appointment.AppointmentStatus status, Pageable pageable);

    /**
     * 查找用户的今日预约
     */
    List<Appointment> findUserTodayAppointments(Long userId);

    /**
     * 查找咨询师的今日预约
     */
    List<Appointment> findCounselorTodayAppointments(Long counselorId);

    /**
     * 查找即将开始的预约
     */
    List<Appointment> findUpcomingAppointments(int hours);

    /**
     * 检查时间冲突
     */
    boolean hasTimeConflict(Long counselorId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 发送预约提醒
     */
    void sendAppointmentReminders();

    /**
     * 统计用户预约总数
     */
    long countUserAppointments(Long userId);

    /**
     * 统计咨询师预约总数
     */
    long countCounselorAppointments(Long counselorId);

    /**
     * 根据状态统计预约数量
     */
    long countAppointmentsByStatus(Appointment.AppointmentStatus status);

    /**
     * 查找指定时间范围内的预约
     */
    Page<Appointment> findAppointmentsByTimeRange(LocalDateTime startTime, 
                                                LocalDateTime endTime, 
                                                Pageable pageable);
}
