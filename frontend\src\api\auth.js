import request from './request'

// 用户登录
export const login = (data) => {
  return request({
    url: '/users/login',
    method: 'post',
    data
  })
}

// 用户注册
export const register = (data) => {
  return request({
    url: '/users/register',
    method: 'post',
    data
  })
}

// 获取用户信息
export const getUserInfo = (userId) => {
  return request({
    url: userId ? `/users/${userId}` : '/users/me',
    method: 'get'
  })
}

// 更新用户信息
export const updateUserInfo = (userId, data) => {
  return request({
    url: `/users/${userId}`,
    method: 'put',
    data
  })
}

// 修改密码
export const changePassword = (userId, data) => {
  return request({
    url: `/users/${userId}/change-password`,
    method: 'post',
    data
  })
}

// 重置密码
export const resetPassword = (data) => {
  return request({
    url: '/users/reset-password',
    method: 'post',
    data
  })
}

// 验证邮箱
export const verifyEmail = (data) => {
  return request({
    url: '/users/verify-email',
    method: 'post',
    data
  })
}

// 验证手机号
export const verifyPhone = (data) => {
  return request({
    url: '/users/verify-phone',
    method: 'post',
    data
  })
}

// 检查邮箱是否存在
export const checkEmailExists = (email) => {
  return request({
    url: '/users/check/email',
    method: 'get',
    params: { email }
  })
}

// 检查用户名是否存在
export const checkUsernameExists = (username) => {
  return request({
    url: '/users/check/username',
    method: 'get',
    params: { username }
  })
}

// 登出
export const logout = () => {
  return request({
    url: '/users/logout',
    method: 'post'
  })
}
