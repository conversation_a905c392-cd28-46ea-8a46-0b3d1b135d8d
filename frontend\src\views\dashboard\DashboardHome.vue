<template>
  <div class="dashboard-home">
    <!-- 欢迎区域 -->
    <div class="welcome-section card">
      <div class="welcome-content">
        <h2>欢迎回来，{{ userStore.userInfo?.realName || userStore.userName }}！</h2>
        <p>{{ getWelcomeMessage() }}</p>
      </div>
      <div class="welcome-actions">
        <router-link to="/counselors" v-if="!userStore.isCounselor">
          <el-button type="primary" size="large">
            <el-icon><Search /></el-icon>
            寻找咨询师
          </el-button>
        </router-link>
        <router-link to="/dashboard/counselor-center" v-else>
          <el-button type="primary" size="large">
            <el-icon><Setting /></el-icon>
            咨询师中心
          </el-button>
        </router-link>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card card">
        <div class="stat-icon">
          <el-icon><Calendar /></el-icon>
        </div>
        <div class="stat-content">
          <h3>{{ stats.totalAppointments }}</h3>
          <p>总预约数</p>
        </div>
      </div>
      
      <div class="stat-card card">
        <div class="stat-icon">
          <el-icon><ChatDotRound /></el-icon>
        </div>
        <div class="stat-content">
          <h3>{{ stats.completedSessions }}</h3>
          <p>完成咨询</p>
        </div>
      </div>
      
      <div class="stat-card card">
        <div class="stat-icon">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="stat-content">
          <h3>{{ stats.upcomingAppointments }}</h3>
          <p>待完成预约</p>
        </div>
      </div>
      
      <div class="stat-card card" v-if="userStore.isCounselor">
        <div class="stat-icon">
          <el-icon><Star /></el-icon>
        </div>
        <div class="stat-content">
          <h3>{{ stats.averageRating || '暂无' }}</h3>
          <p>平均评分</p>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-section">
      <div class="recent-appointments card">
        <div class="section-header">
          <h3>最近预约</h3>
          <router-link to="/dashboard/appointments">
            <el-button type="text">查看全部</el-button>
          </router-link>
        </div>
        
        <div class="appointments-list" v-loading="appointmentsLoading">
          <div 
            v-for="appointment in recentAppointments" 
            :key="appointment.id"
            class="appointment-item"
          >
            <div class="appointment-info">
              <div class="appointment-avatar">
                <el-avatar :size="40" :src="getAppointmentAvatar(appointment)">
                  {{ getAppointmentName(appointment).charAt(0) }}
                </el-avatar>
              </div>
              <div class="appointment-details">
                <h4>{{ getAppointmentName(appointment) }}</h4>
                <p>{{ formatDateTime(appointment.scheduledTime) }}</p>
              </div>
            </div>
            <div class="appointment-status">
              <el-tag :type="getStatusType(appointment.status)">
                {{ getStatusText(appointment.status) }}
              </el-tag>
            </div>
          </div>
          
          <div v-if="!appointmentsLoading && recentAppointments.length === 0" class="empty-state">
            <el-empty description="暂无预约记录" :image-size="80" />
          </div>
        </div>
      </div>
      
      <div class="quick-actions card">
        <div class="section-header">
          <h3>快捷操作</h3>
        </div>
        
        <div class="actions-list">
          <div class="action-item" @click="goToProfile">
            <el-icon><User /></el-icon>
            <span>编辑个人信息</span>
            <el-icon><ArrowRight /></el-icon>
          </div>
          
          <div class="action-item" @click="goToAppointments">
            <el-icon><Calendar /></el-icon>
            <span>管理预约</span>
            <el-icon><ArrowRight /></el-icon>
          </div>
          
          <div class="action-item" @click="goToSessions">
            <el-icon><ChatDotRound /></el-icon>
            <span>查看咨询记录</span>
            <el-icon><ArrowRight /></el-icon>
          </div>
          
          <div 
            class="action-item" 
            @click="goToCounselorApply"
            v-if="!userStore.isCounselor"
          >
            <el-icon><UserFilled /></el-icon>
            <span>申请成为咨询师</span>
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { 
  Search, Setting, Calendar, ChatDotRound, Clock, Star,
  User, ArrowRight, UserFilled
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const router = useRouter()
const userStore = useUserStore()

const appointmentsLoading = ref(false)
const recentAppointments = ref([])

// 统计数据
const stats = reactive({
  totalAppointments: 0,
  completedSessions: 0,
  upcomingAppointments: 0,
  averageRating: null
})

// 获取欢迎消息
const getWelcomeMessage = () => {
  const hour = new Date().getHours()
  let greeting = ''
  
  if (hour < 12) {
    greeting = '早上好'
  } else if (hour < 18) {
    greeting = '下午好'
  } else {
    greeting = '晚上好'
  }
  
  if (userStore.isCounselor) {
    return `${greeting}！今天有什么新的咨询安排吗？`
  } else {
    return `${greeting}！希望您今天心情愉快！`
  }
}

// 获取预约相关信息
const getAppointmentAvatar = (appointment) => {
  if (userStore.isCounselor) {
    return appointment.user?.avatarUrl
  } else {
    return appointment.counselor?.user?.avatarUrl
  }
}

const getAppointmentName = (appointment) => {
  if (userStore.isCounselor) {
    return appointment.user?.realName || appointment.user?.username || '用户'
  } else {
    return appointment.counselor?.user?.realName || appointment.counselor?.user?.username || '咨询师'
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('MM月DD日 HH:mm')
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'CONFIRMED': 'success',
    'IN_PROGRESS': 'primary',
    'COMPLETED': 'info',
    'CANCELLED': 'danger',
    'NO_SHOW': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待确认',
    'CONFIRMED': '已确认',
    'IN_PROGRESS': '进行中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消',
    'NO_SHOW': '未出席'
  }
  return statusMap[status] || status
}

// 获取统计数据
const fetchStats = async () => {
  try {
    // 这里应该调用API获取统计数据
    // 暂时使用模拟数据
    stats.totalAppointments = 12
    stats.completedSessions = 8
    stats.upcomingAppointments = 2
    if (userStore.isCounselor) {
      stats.averageRating = 4.8
    }
  } catch (error) {
    console.error('Failed to fetch stats:', error)
  }
}

// 获取最近预约
const fetchRecentAppointments = async () => {
  try {
    appointmentsLoading.value = true
    // 这里应该调用API获取最近预约
    // 暂时使用模拟数据
    recentAppointments.value = []
  } catch (error) {
    console.error('Failed to fetch recent appointments:', error)
  } finally {
    appointmentsLoading.value = false
  }
}

// 快捷操作
const goToProfile = () => {
  router.push('/dashboard/profile')
}

const goToAppointments = () => {
  router.push('/dashboard/appointments')
}

const goToSessions = () => {
  router.push('/dashboard/sessions')
}

const goToCounselorApply = () => {
  router.push('/dashboard/counselor-apply')
}

onMounted(() => {
  fetchStats()
  fetchRecentAppointments()
})
</script>

<style lang="scss" scoped>
.dashboard-home {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  padding: 40px;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  
  h2 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 10px;
  }
  
  p {
    font-size: 1.1rem;
    opacity: 0.9;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  padding: 25px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  
  .el-icon {
    font-size: 1.5rem;
  }
}

.stat-content {
  h3 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 5px;
  }
  
  p {
    color: var(--text-secondary);
    font-size: 0.9rem;
  }
}

.recent-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-light);
  
  h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
  }
}

.recent-appointments {
  padding: 25px;
}

.appointments-list {
  min-height: 200px;
}

.appointment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0;
  border-bottom: 1px solid var(--border-light);
  
  &:last-child {
    border-bottom: none;
  }
}

.appointment-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.appointment-details {
  h4 {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 5px;
  }
  
  p {
    color: var(--text-secondary);
    font-size: 0.9rem;
  }
}

.quick-actions {
  padding: 25px;
}

.actions-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: var(--bg-lighter);
    color: var(--primary-color);
  }
  
  .el-icon:first-child {
    color: var(--primary-color);
    font-size: 1.2rem;
  }
  
  span {
    flex: 1;
    font-size: 0.95rem;
  }
  
  .el-icon:last-child {
    color: var(--text-placeholder);
    font-size: 1rem;
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

// 响应式设计
@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .recent-section {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: 20px;
  }
  
  .stat-icon {
    width: 50px;
    height: 50px;
  }
  
  .stat-content h3 {
    font-size: 1.5rem;
  }
}
</style>
