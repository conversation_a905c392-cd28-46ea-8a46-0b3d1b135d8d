# 线上心理咨询服务平台 - 主配置文件
server:
  port: 8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  # 应用基本信息
  application:
    name: psychology-platform
  

  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************
    username: root
    password: "021026"

    # 连接池配置
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: PsychologyPlatformHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
      naming:
        physical-strategy: org.hibernate.boot.model.naming.SnakeCasePhysicalNamingStrategy
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
    show-sql: true
    format-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        generate_statistics: false

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
      enabled: true

# 日志配置
logging:
  level:
    com.psychology.platform: INFO
    org.springframework.security: INFO
    org.hibernate.SQL: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/psychology-platform.log
    max-size: 100MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# SpringDoc OpenAPI配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: 线上心理咨询服务平台 API
    description: 提供用户管理、咨询师管理、预约系统等功能的RESTful API
    version: 1.0.0
    contact:
      name: Psychology Platform Team
      email: <EMAIL>

# 自定义配置
psychology:
  # JWT配置
  jwt:
    secret: ${JWT_SECRET:psychology-platform-jwt-secret-key-2024}
    expiration: 86400000  # 24小时 (毫秒)
    refresh-expiration: 604800000  # 7天 (毫秒)
  
  # 文件存储配置
  file:
    upload-path: ${FILE_UPLOAD_PATH:./uploads}
    max-size: 10485760  # 10MB
    allowed-types: jpg,jpeg,png,pdf,doc,docx
  
  # 业务配置
  business:
    # 预约相关配置
    appointment:
      advance-days: 30  # 最多提前预约天数
      cancel-hours: 24  # 取消预约最少提前小时数
    
    # 咨询会话配置
    session:
      max-duration: 60  # 最大会话时长(分钟)
      reminder-minutes: 5  # 会话结束提醒时间(分钟)
