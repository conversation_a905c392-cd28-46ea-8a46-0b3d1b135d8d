<template>
  <div class="not-found-page">
    <div class="not-found-container">
      <div class="not-found-content">
        <div class="error-code">404</div>
        <h1>页面未找到</h1>
        <p>抱歉，您访问的页面不存在或已被移除。</p>
        
        <div class="error-actions">
          <el-button type="primary" size="large" @click="goHome">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          <el-button size="large" @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回上页
          </el-button>
        </div>
        
        <div class="helpful-links">
          <h3>您可能在寻找：</h3>
          <ul>
            <li><router-link to="/counselors">咨询师列表</router-link></li>
            <li><router-link to="/dashboard">个人中心</router-link></li>
            <li><router-link to="/login">用户登录</router-link></li>
            <li><router-link to="/register">用户注册</router-link></li>
          </ul>
        </div>
      </div>
      
      <div class="not-found-illustration">
        <div class="illustration-content">
          <div class="floating-elements">
            <div class="element element-1"></div>
            <div class="element element-2"></div>
            <div class="element element-3"></div>
          </div>
          <div class="main-illustration">
            <el-icon class="search-icon"><Search /></el-icon>
            <p>页面走丢了...</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { House, ArrowLeft, Search } from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.not-found-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.not-found-container {
  display: flex;
  align-items: center;
  max-width: 1000px;
  width: 100%;
  gap: 60px;
}

.not-found-content {
  flex: 1;
  color: white;
}

.error-code {
  font-size: 8rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 20px;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.6));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.not-found-content h1 {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 15px;
}

.not-found-content p {
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.9;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 20px;
  margin-bottom: 50px;
  flex-wrap: wrap;
}

.helpful-links {
  h3 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    opacity: 0.9;
  }
  
  ul {
    list-style: none;
    padding: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
  }
  
  li {
    a {
      color: rgba(255, 255, 255, 0.8);
      text-decoration: none;
      padding: 8px 0;
      display: block;
      transition: all 0.3s ease;
      border-bottom: 1px solid transparent;
      
      &:hover {
        color: white;
        border-bottom-color: rgba(255, 255, 255, 0.5);
      }
    }
  }
}

.not-found-illustration {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.illustration-content {
  position: relative;
  width: 300px;
  height: 300px;
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.element {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
  
  &.element-1 {
    width: 60px;
    height: 60px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
  }
  
  &.element-2 {
    width: 40px;
    height: 40px;
    top: 60%;
    right: 20%;
    animation-delay: 2s;
  }
  
  &.element-3 {
    width: 80px;
    height: 80px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
  }
}

.main-illustration {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
}

.search-icon {
  font-size: 6rem;
  margin-bottom: 20px;
  opacity: 0.7;
  animation: pulse 2s ease-in-out infinite;
}

.main-illustration p {
  font-size: 1.2rem;
  opacity: 0.8;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .not-found-container {
    flex-direction: column;
    text-align: center;
    gap: 40px;
  }
  
  .error-code {
    font-size: 6rem;
  }
  
  .not-found-content h1 {
    font-size: 2rem;
  }
  
  .not-found-content p {
    font-size: 1.1rem;
  }
  
  .error-actions {
    justify-content: center;
  }
  
  .illustration-content {
    width: 250px;
    height: 250px;
  }
  
  .search-icon {
    font-size: 4rem;
  }
  
  .helpful-links ul {
    grid-template-columns: 1fr;
    text-align: left;
  }
}
</style>
