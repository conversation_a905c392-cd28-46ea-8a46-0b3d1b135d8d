<template>
  <div class="counselor-detail-page">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="nav-container">
        <router-link to="/" class="nav-brand">
          <h2>心理咨询平台</h2>
        </router-link>
        <div class="nav-menu">
          <router-link to="/" class="nav-link">首页</router-link>
          <router-link to="/counselors" class="nav-link">咨询师</router-link>
          <template v-if="!userStore.isLoggedIn">
            <router-link to="/login" class="nav-link">登录</router-link>
            <router-link to="/register" class="nav-link nav-button">注册</router-link>
          </template>
          <template v-else>
            <router-link to="/dashboard" class="nav-link">个人中心</router-link>
            <el-button @click="handleLogout" type="text">退出</el-button>
          </template>
        </div>
      </div>
    </nav>

    <div class="page-container" v-loading="loading">
      <div class="content-wrapper" v-if="counselor">
        <!-- 咨询师基本信息 -->
        <div class="counselor-header card">
          <div class="counselor-avatar">
            <el-avatar :size="120" :src="counselor.user?.avatarUrl">
              {{ counselor.user?.username?.charAt(0) }}
            </el-avatar>
            <div class="availability-badge" v-if="counselor.availableForBooking">
              <el-icon><CircleCheck /></el-icon>
              可预约
            </div>
          </div>
          
          <div class="counselor-info">
            <h1>{{ counselor.user?.realName || counselor.user?.username }}</h1>
            <p class="specialization">{{ getSpecializationText(counselor.specialization) }}</p>
            
            <div class="counselor-meta">
              <div class="meta-item">
                <el-icon><Star /></el-icon>
                <span>{{ counselor.rating || '暂无评分' }}</span>
                <span class="sessions-count">({{ counselor.totalSessions }}次咨询)</span>
              </div>
              <div class="meta-item">
                <el-icon><Trophy /></el-icon>
                <span>{{ counselor.yearsOfExperience || 0 }}年经验</span>
              </div>
              <div class="meta-item">
                <el-icon><School /></el-icon>
                <span>{{ counselor.education || '暂无信息' }}</span>
              </div>
            </div>
            
            <div class="price-info">
              <span class="price">¥{{ counselor.hourlyRate }}</span>
              <span class="price-unit">/小时</span>
            </div>
          </div>
          
          <div class="counselor-actions">
            <el-button 
              type="primary" 
              size="large"
              :disabled="!counselor.availableForBooking"
              @click="handleBooking"
            >
              <el-icon><Calendar /></el-icon>
              {{ counselor.availableForBooking ? '立即预约' : '暂不可约' }}
            </el-button>
            <el-button size="large" @click="handleContact">
              <el-icon><ChatDotRound /></el-icon>
              联系咨询师
            </el-button>
          </div>
        </div>

        <!-- 详细信息 -->
        <div class="counselor-details">
          <div class="details-main">
            <!-- 专业简介 -->
            <div class="detail-section card">
              <h3>专业简介</h3>
              <p>{{ counselor.professionalSummary || '暂无简介' }}</p>
            </div>
            
            <!-- 专业资质 -->
            <div class="detail-section card">
              <h3>专业资质</h3>
              <div class="qualification-item">
                <strong>执业证书编号：</strong>
                <span>{{ counselor.licenseNumber }}</span>
              </div>
              <div class="qualification-item">
                <strong>认证状态：</strong>
                <el-tag :type="getStatusType(counselor.status)">
                  {{ getStatusText(counselor.status) }}
                </el-tag>
              </div>
              <div class="qualification-item" v-if="counselor.verifiedAt">
                <strong>认证时间：</strong>
                <span>{{ formatDate(counselor.verifiedAt) }}</span>
              </div>
            </div>
            
            <!-- 服务时间 -->
            <div class="detail-section card">
              <h3>服务时间</h3>
              <p>周一至周日 9:00-21:00</p>
              <p class="note">具体时间可在预约时协商调整</p>
            </div>
          </div>
          
          <div class="details-sidebar">
            <!-- 快速预约 -->
            <div class="quick-booking card">
              <h3>快速预约</h3>
              <div class="booking-form">
                <el-date-picker
                  v-model="bookingDate"
                  type="datetime"
                  placeholder="选择预约时间"
                  :disabled-date="disabledDate"
                  :disabled-hours="disabledHours"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%; margin-bottom: 15px;"
                />
                <el-input
                  v-model="bookingNotes"
                  type="textarea"
                  placeholder="请简要描述您的咨询需求..."
                  :rows="3"
                  maxlength="200"
                  show-word-limit
                  style="margin-bottom: 15px;"
                />
                <el-button 
                  type="primary" 
                  :disabled="!counselor.availableForBooking || !bookingDate"
                  @click="handleQuickBooking"
                  style="width: 100%;"
                >
                  确认预约
                </el-button>
              </div>
            </div>
            
            <!-- 联系方式 -->
            <div class="contact-info card">
              <h3>联系方式</h3>
              <div class="contact-item">
                <el-icon><Message /></el-icon>
                <span>平台内消息</span>
              </div>
              <div class="contact-item">
                <el-icon><VideoCamera /></el-icon>
                <span>视频咨询</span>
              </div>
              <div class="contact-item">
                <el-icon><Phone /></el-icon>
                <span>语音咨询</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="!loading" class="empty-state">
        <el-empty description="咨询师信息不存在">
          <router-link to="/counselors">
            <el-button type="primary">返回咨询师列表</el-button>
          </router-link>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { getCounselorDetail } from '@/api/counselor'
import { createAppointment } from '@/api/appointment'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  CircleCheck, Star, Trophy, School, Calendar, ChatDotRound,
  Message, VideoCamera, Phone
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const counselor = ref(null)
const bookingDate = ref('')
const bookingNotes = ref('')

// 专业领域映射
const specializationMap = {
  'ANXIETY_DISORDERS': '焦虑障碍',
  'DEPRESSION': '抑郁症',
  'RELATIONSHIP_COUNSELING': '情感咨询',
  'FAMILY_THERAPY': '家庭治疗',
  'CHILD_PSYCHOLOGY': '儿童心理',
  'ADDICTION_COUNSELING': '成瘾咨询',
  'TRAUMA_THERAPY': '创伤治疗',
  'CAREER_COUNSELING': '职业咨询',
  'STRESS_MANAGEMENT': '压力管理',
  'GENERAL_COUNSELING': '综合咨询'
}

const getSpecializationText = (specialization) => {
  return specializationMap[specialization] || specialization
}

const getStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger',
    'SUSPENDED': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待审核',
    'APPROVED': '已认证',
    'REJECTED': '已拒绝',
    'SUSPENDED': '已暂停'
  }
  return statusMap[status] || status
}

const formatDate = (date) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

// 禁用过去的日期
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 8.64e7 // 禁用昨天及之前的日期
}

// 禁用非工作时间
const disabledHours = () => {
  const hours = []
  for (let i = 0; i < 9; i++) {
    hours.push(i)
  }
  for (let i = 21; i < 24; i++) {
    hours.push(i)
  }
  return hours
}

// 获取咨询师详情
const fetchCounselorDetail = async () => {
  try {
    loading.value = true
    const response = await getCounselorDetail(route.params.id)
    counselor.value = response.data || response
  } catch (error) {
    console.error('Failed to fetch counselor detail:', error)
    ElMessage.error('获取咨询师信息失败')
  } finally {
    loading.value = false
  }
}

// 处理预约
const handleBooking = () => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  router.push(`/booking/${route.params.id}`)
}

// 快速预约
const handleQuickBooking = async () => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  
  if (!bookingDate.value) {
    ElMessage.warning('请选择预约时间')
    return
  }
  
  try {
    await ElMessageBox.confirm('确认创建此预约吗？', '确认预约', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    const appointmentData = {
      userId: userStore.userInfo.id,
      counselorId: counselor.value.id,
      scheduledTime: bookingDate.value,
      notes: bookingNotes.value
    }
    
    await createAppointment(appointmentData)
    ElMessage.success('预约创建成功')
    router.push('/dashboard/appointments')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to create appointment:', error)
      ElMessage.error('预约创建失败')
    }
  }
}

// 联系咨询师
const handleContact = () => {
  ElMessage.info('联系功能待实现')
}

// 退出登录
const handleLogout = async () => {
  await userStore.logoutUser()
  router.push('/')
}

onMounted(() => {
  fetchCounselorDetail()
})
</script>

<style lang="scss" scoped>
// 导航栏样式（复用）
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.nav-brand {
  text-decoration: none;
  
  h2 {
    color: var(--primary-color);
    font-weight: 600;
  }
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 30px;
}

.nav-link {
  color: var(--text-regular);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  
  &:hover {
    color: var(--primary-color);
  }
  
  &.nav-button {
    background: var(--primary-color);
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    
    &:hover {
      background: var(--primary-dark);
      color: white;
    }
  }
}

.page-container {
  background: var(--bg-white);
  min-height: calc(100vh - 70px);
  padding: 40px 20px;
}

.counselor-header {
  display: flex;
  align-items: flex-start;
  gap: 40px;
  padding: 40px;
  margin-bottom: 30px;
  position: relative;
}

.counselor-avatar {
  position: relative;
  flex-shrink: 0;
}

.availability-badge {
  position: absolute;
  bottom: -5px;
  right: -5px;
  background: var(--success-color);
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.counselor-info {
  flex: 1;
  
  h1 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 10px;
  }
  
  .specialization {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 20px;
  }
}

.counselor-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 25px;
  margin-bottom: 20px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-regular);
  
  .el-icon {
    color: var(--primary-color);
  }
  
  .sessions-count {
    color: var(--text-secondary);
    font-size: 0.9rem;
  }
}

.price-info {
  .price {
    font-size: 2rem;
    font-weight: 600;
    color: var(--primary-color);
  }
  
  .price-unit {
    color: var(--text-secondary);
    margin-left: 5px;
  }
}

.counselor-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-self: center;
}

.counselor-details {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
}

.details-main {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-section {
  padding: 30px;
  
  h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 20px;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
  }
  
  p {
    line-height: 1.8;
    color: var(--text-regular);
    
    &.note {
      color: var(--text-secondary);
      font-size: 0.9rem;
      margin-top: 10px;
    }
  }
}

.qualification-item {
  margin-bottom: 15px;
  
  strong {
    color: var(--text-primary);
    margin-right: 10px;
  }
}

.details-sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.quick-booking,
.contact-info {
  padding: 25px;
  
  h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 20px;
  }
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 0;
  color: var(--text-regular);
  
  .el-icon {
    color: var(--primary-color);
    font-size: 1.2rem;
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

// 响应式设计
@media (max-width: 768px) {
  .counselor-header {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .counselor-actions {
    align-self: stretch;
  }
  
  .counselor-details {
    grid-template-columns: 1fr;
  }
  
  .counselor-meta {
    justify-content: center;
  }
}
</style>
