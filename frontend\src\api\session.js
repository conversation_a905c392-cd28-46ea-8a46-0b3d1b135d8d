import request from './request'

// 创建咨询会话
export const createSession = (appointmentId) => {
  return request({
    url: '/sessions',
    method: 'post',
    data: { appointmentId }
  })
}

// 获取会话详情
export const getSessionDetail = (id) => {
  return request({
    url: `/sessions/${id}`,
    method: 'get'
  })
}

// 根据预约ID获取会话
export const getSessionByAppointmentId = (appointmentId) => {
  return request({
    url: `/sessions/appointment/${appointmentId}`,
    method: 'get'
  })
}

// 获取用户会话列表
export const getUserSessions = (userId, params) => {
  return request({
    url: `/sessions/user/${userId}`,
    method: 'get',
    params
  })
}

// 获取咨询师会话列表
export const getCounselorSessions = (counselorId, params) => {
  return request({
    url: `/sessions/counselor/${counselorId}`,
    method: 'get',
    params
  })
}

// 根据状态获取会话列表
export const getSessionsByStatus = (status, params) => {
  return request({
    url: `/sessions/status/${status}`,
    method: 'get',
    params
  })
}

// 开始会话
export const startSession = (id) => {
  return request({
    url: `/sessions/${id}/start`,
    method: 'post'
  })
}

// 结束会话
export const endSession = (id, data) => {
  return request({
    url: `/sessions/${id}/end`,
    method: 'post',
    data
  })
}

// 取消会话
export const cancelSession = (id) => {
  return request({
    url: `/sessions/${id}/cancel`,
    method: 'post'
  })
}

// 添加用户反馈
export const addUserFeedback = (id, data) => {
  return request({
    url: `/sessions/${id}/feedback`,
    method: 'post',
    data
  })
}

// 标记为紧急情况
export const markAsEmergency = (id, data) => {
  return request({
    url: `/sessions/${id}/emergency`,
    method: 'post',
    data
  })
}

// 设置跟进要求
export const setFollowUpRequired = (id, data) => {
  return request({
    url: `/sessions/${id}/follow-up`,
    method: 'post',
    data
  })
}

// 获取需要跟进的会话
export const getSessionsNeedingFollowUp = () => {
  return request({
    url: '/sessions/follow-up',
    method: 'get'
  })
}

// 获取紧急情况会话
export const getEmergencySessions = (params) => {
  return request({
    url: '/sessions/emergency',
    method: 'get',
    params
  })
}

// 获取今日进行中的会话
export const getTodayInProgressSessions = () => {
  return request({
    url: '/sessions/today/in-progress',
    method: 'get'
  })
}

// 获取已完成但未评分的会话
export const getCompletedSessionsWithoutRating = (params) => {
  return request({
    url: '/sessions/completed/no-rating',
    method: 'get',
    params
  })
}

// 统计用户完成的会话数
export const getUserCompletedSessionsCount = (userId) => {
  return request({
    url: `/sessions/stats/user/${userId}/completed`,
    method: 'get'
  })
}

// 统计咨询师完成的会话数
export const getCounselorCompletedSessionsCount = (counselorId) => {
  return request({
    url: `/sessions/stats/counselor/${counselorId}/completed`,
    method: 'get'
  })
}

// 计算咨询师平均评分
export const getCounselorAverageRating = (counselorId) => {
  return request({
    url: `/sessions/stats/counselor/${counselorId}/rating`,
    method: 'get'
  })
}
