<template>
  <div class="auth-page">
    <div class="auth-container">
      <div class="auth-card card">
        <div class="auth-header">
          <h2>创建账户</h2>
          <p>加入我们，开始您的心理健康之旅</p>
        </div>
        
        <el-form 
          ref="registerFormRef"
          :model="registerForm" 
          :rules="registerRules"
          class="auth-form"
          @submit.prevent="handleRegister"
        >
          <el-form-item prop="username">
            <el-input
              v-model="registerForm.username"
              placeholder="请输入用户名"
              size="large"
              :prefix-icon="User"
            />
          </el-form-item>
          
          <el-form-item prop="email">
            <el-input
              v-model="registerForm.email"
              placeholder="请输入邮箱"
              size="large"
              :prefix-icon="Message"
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              placeholder="请输入密码"
              size="large"
              :prefix-icon="Lock"
              show-password
            />
          </el-form-item>
          
          <el-form-item prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="请确认密码"
              size="large"
              :prefix-icon="Lock"
              show-password
            />
          </el-form-item>
          
          <el-form-item prop="agreement">
            <el-checkbox v-model="registerForm.agreement">
              我已阅读并同意 
              <el-button type="text" @click="showTerms">《服务条款》</el-button>
              和
              <el-button type="text" @click="showPrivacy">《隐私政策》</el-button>
            </el-checkbox>
          </el-form-item>
          
          <el-form-item>
            <el-button 
              type="primary" 
              size="large" 
              :loading="loading"
              @click="handleRegister"
              class="auth-button"
            >
              注册
            </el-button>
          </el-form-item>
        </el-form>
        
        <div class="auth-footer">
          <p>已有账户？ 
            <router-link to="/login" class="auth-link">立即登录</router-link>
          </p>
        </div>
      </div>
      
      <div class="auth-side">
        <div class="side-content">
          <h3>开始您的心理健康之旅</h3>
          <p>注册账户，享受专业的心理咨询服务</p>
          <div class="side-stats">
            <div class="stat-item">
              <div class="stat-number">1000+</div>
              <div class="stat-label">专业咨询师</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">50000+</div>
              <div class="stat-label">服务用户</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">98%</div>
              <div class="stat-label">满意度</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { checkEmailExists, checkUsernameExists } from '@/api/auth'
import { ElMessage } from 'element-plus'
import { User, Lock, Message } from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const registerFormRef = ref()

// 注册表单
const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreement: false
})

// 自定义验证器
const validateUsername = async (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入用户名'))
    return
  }
  
  if (value.length < 2 || value.length > 20) {
    callback(new Error('用户名长度必须在2-20个字符之间'))
    return
  }
  
  if (!/^[a-zA-Z0-9_]+$/.test(value)) {
    callback(new Error('用户名只能包含字母、数字和下划线'))
    return
  }
  
  try {
    const response = await checkUsernameExists(value)
    if (response.data || response) {
      callback(new Error('用户名已存在'))
    } else {
      callback()
    }
  } catch (error) {
    callback()
  }
}

const validateEmail = async (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入邮箱'))
    return
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(value)) {
    callback(new Error('请输入正确的邮箱格式'))
    return
  }
  
  try {
    const response = await checkEmailExists(value)
    if (response.data || response) {
      callback(new Error('邮箱已被注册'))
    } else {
      callback()
    }
  } catch (error) {
    callback()
  }
}

const validatePassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入密码'))
    return
  }
  
  if (value.length < 6) {
    callback(new Error('密码长度不能少于6位'))
    return
  }
  
  if (!/(?=.*[A-Za-z])(?=.*\d)/.test(value)) {
    callback(new Error('密码必须包含字母和数字'))
    return
  }
  
  callback()
}

const validateConfirmPassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请确认密码'))
    return
  }
  
  if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
    return
  }
  
  callback()
}

const validateAgreement = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请阅读并同意服务条款和隐私政策'))
    return
  }
  callback()
}

// 表单验证规则
const registerRules = {
  username: [{ validator: validateUsername, trigger: 'blur' }],
  email: [{ validator: validateEmail, trigger: 'blur' }],
  password: [{ validator: validatePassword, trigger: 'blur' }],
  confirmPassword: [{ validator: validateConfirmPassword, trigger: 'blur' }],
  agreement: [{ validator: validateAgreement, trigger: 'change' }]
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return
  
  try {
    await registerFormRef.value.validate()
    loading.value = true
    
    const { confirmPassword, agreement, ...userData } = registerForm
    const success = await userStore.registerUser(userData)
    
    if (success) {
      router.push('/login')
    }
  } catch (error) {
    console.error('Register validation failed:', error)
  } finally {
    loading.value = false
  }
}

// 显示服务条款
const showTerms = () => {
  ElMessage.info('服务条款功能待实现')
}

// 显示隐私政策
const showPrivacy = () => {
  ElMessage.info('隐私政策功能待实现')
}
</script>

<style lang="scss" scoped>
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.auth-container {
  display: flex;
  max-width: 900px;
  width: 100%;
  min-height: 700px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.auth-card {
  flex: 1;
  padding: 50px 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: var(--bg-white);
  border-radius: 0;
  box-shadow: none;
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.auth-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 10px;
}

.auth-header p {
  color: var(--text-secondary);
  font-size: 1rem;
}

.auth-form {
  .el-form-item {
    margin-bottom: 20px;
  }
}

.auth-button {
  width: 100%;
  height: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 12px;
}

.auth-footer {
  text-align: center;
  margin-top: 20px;
  
  p {
    color: var(--text-secondary);
  }
}

.auth-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  
  &:hover {
    text-decoration: underline;
  }
}

.auth-side {
  flex: 1;
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  }
}

.side-content {
  text-align: center;
  padding: 40px;
  position: relative;
  z-index: 1;
}

.side-content h3 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.side-content p {
  font-size: 1.1rem;
  margin-bottom: 40px;
  opacity: 0.9;
  line-height: 1.6;
}

.side-stats {
  display: flex;
  justify-content: space-around;
  gap: 20px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

// 响应式设计
@media (max-width: 768px) {
  .auth-container {
    flex-direction: column;
    max-width: 400px;
  }
  
  .auth-side {
    order: -1;
    min-height: 200px;
  }
  
  .auth-card {
    padding: 30px 25px;
  }
  
  .side-content {
    padding: 20px;
  }
  
  .side-content h3 {
    font-size: 1.5rem;
  }
  
  .side-stats {
    flex-direction: column;
    gap: 15px;
  }
  
  .stat-number {
    font-size: 1.5rem;
  }
}
</style>
